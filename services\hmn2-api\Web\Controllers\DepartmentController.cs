﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Models.Controller.Department;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 部門
/// </summary>
[Route("[controller]")]
[Authorize]
public class DepartmentController(IDataAccessService dataAccessService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{

    /// <summary>
    /// 新增部門
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("departments")]
    [RequestParamListDuplicate("DeptCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateDepartment([FromBody] List<CreateDepartment> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        _logService.Logging("info", logActionName, requestUUID, "Department Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        foreach (CreateDepartment dept in paramList)
        {
            Department department = new Department
            {
                AppCode = _user.AppCode,
                AreaCode = dept.AreaCode,
                //BuildingCode = dept.BuildingCode,
                CustomDeptCode = dept.DeptCode,
                DeptCode = dept.DeptCode,
                DeptName = dept.DeptName,
                Enable = true,
                IsManagedDept = dept.IsManagedDept == "Y",
                IsUsageDept = dept.IsUsageDept == "Y",
                //PlaneCode = dept.PlaneCode,
                // SectorCode = dept.SectorCode,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            if (dept.SectorCodes != null && dept.SectorCodes.Count > 0)
            {
                foreach (var sectorCode in dept.SectorCodes)
                {
                    var departSector = new DepartSector
                    {
                        AppCode = _user.AppCode,
                        AreaCode = dept.AreaCode,
                        DeptCode = dept.DeptCode,
                        SectorCode = sectorCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account,
                        ModifyDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync<DepartSector>(departSector);
                }
            }

            await _dataAccessService.CreateAsync<Department>(department);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Department Data append done.");

        returnModel = new ReturnModel
        {
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("departments")]
    [RequestParamListDuplicate("DeptCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateDepartment([FromBody] List<UpdateDepartment> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        _dataAccessService.BeginTransaction();

        foreach (UpdateDepartment param in paramList)
        {
            Department department = _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.DeptCode == param.DeptCode).AsTracking().FirstOrDefault();

            var deptCode = param.DeptCode;

            if (department == null)
            {
                _logService.Logging("error", logActionName, requestUUID, $"deptNotFound");
                returnModel = new ReturnModel
                {
                    httpStatus = StatusCodes.Status404NotFound,
                    result = false,
                    data = new ReturnError { index = 1, code = deptCode, errors = [new ErrorDetail { index = 1, error = "err.notFound.departments.Department.DeptCode" }] }
                };
                return StatusCode(returnModel.httpStatus, returnModel);
            }

            List<string> updateField = new List<string>();

            if (param.IsManagedDept != null)
            {
                updateField.Add("IsManagedDept");
                department.IsManagedDept = param.IsManagedDept == "Y";
            }

            if (param.IsUsageDept != null)
            {
                updateField.Add("IsUsageDept");
                department.IsUsageDept = param.IsUsageDept == "Y";
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                department.Enable = param.Enable == "Y";
            }

            if (param.DeptName != null)
            {
                updateField.Add("DeptName");
                department.DeptName = param.DeptName;
            }

            // if (param.BuildingCode != null)
            // {
            //     updateField.Add("BuildingCode");
            //     department.BuildingCode = param.BuildingCode;
            // }

            // if (param.PlaneCode != null)
            // {
            //     updateField.Add("PlaneCode");
            //     department.PlaneCode = param.PlaneCode;
            // }

            await _dataAccessService.DeleteAsync<DepartSector>(d => d.AppCode == _user.AppCode && d.AreaCode == param.AreaCode && d.DeptCode == param.DeptCode);
            if (param.SectorCodes != null && param.SectorCodes.Count > 0)
            {
                foreach (var sectorCode in param.SectorCodes)
                {
                    await _dataAccessService.CreateAsync<DepartSector>(new DepartSector
                    {
                        AppCode = _user.AppCode,
                        AreaCode = param.AreaCode,
                        DeptCode = param.DeptCode,
                        SectorCode = sectorCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account,
                        ModifyDate = DateTime.Now
                    });
                }

                // TODO: 這段程式碼有問題，留下來待解決
                //Expression<Func<DepartSector, bool>> predicate = u =>u.DeptCode == param.DeptCode;
                //Expression<Func<DepartSector, DepartSector>> updateExpression = u => new DepartSector { 
                //    SectorCode = param.SectorCode//, 
                //    ModifyUserAccount = _user.Account, 
                //    ModifyDate = DateTime.Now 
                //};
                //await _dataAccessService.UpdateAsync<DepartSector>(predicate, updateExpression);
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            department.ModifyDate = DateTime.Now;
            department.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<Department>(department, updateField.ToArray());
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Department Data update done.");

        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }

    [HttpDelete("departments")]
    [RequestParamListDuplicate("DeptCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteDepartment([FromBody] List<DeleteDepartment> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除部門資料
        _logService.Logging("info", logActionName, requestUUID, "Department Data Validated, start delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteDepartment dept in paramList)
        {
            await _dataAccessService.DeleteAsync<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == dept.AreaCode && e.DeptCode == dept.DeptCode);
            await _dataAccessService.DeleteAsync<CameraDepartment>(e => e.AppCode == _user.AppCode && e.AreaCode == dept.AreaCode && e.DeptCode == dept.DeptCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Department Data delete done.");

        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }

    [HttpGet("departments")]
    public async Task<IActionResult> RetrieveDepartment([FromQuery] RetrieveDepartment param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)
        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];


        string[] deptCodeList = param.DeptCodeList != null && param.DeptCodeList.Contains(",") ? param.DeptCodeList.Split(",") : [];

        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var buildingList = _dataAccessService.Fetch<Building>(e => e.AppCode == _user.AppCode);
        var deptList = _dataAccessService.Fetch<Department>(x => x.AppCode == _user.AppCode && (x.Enable == true || x.Enable == false)).OrderByDescending(x => x.ModifyDate).ThenByDescending(x => x.CreateDate);
        var planeList = _dataAccessService.Fetch<Plane>(e => e.AppCode == _user.AppCode);
        var sectorList = _dataAccessService.Fetch<Sector>(e => e.AppCode == _user.AppCode);
        var userDataList = _dataAccessService.Fetch<UserDatum>(e => e.AppCode == _user.AppCode && e.IsSupervisor == true);
        var departSectorList = _dataAccessService.Fetch<DepartSector>(e => e.AppCode == _user.AppCode);
        var query = (from a in deptList
                     join c in areaList on a.AreaCode equals c.AreaCode
                     join d in userDataList on a.DeptCode equals d.DeptCode into temp2
                     select new
                     {
                         a.Id,
                         Enable = (a.Enable != null && a.Enable == true) ? "Y" : "N",
                         a.DeptCode,
                         a.CustomDeptCode,
                         a.DeptName,
                         Sectors = (from ds in departSectorList
                                    where ds.DeptCode == a.DeptCode && ds.AreaCode == a.AreaCode
                                    join s in sectorList on ds.SectorCode equals s.SectorCode
                                    select new
                                    {
                                        Enable = (s.Enable != null && s.Enable == true) ? "Y" : "N",
                                        SectorCode = s.SectorCode,
                                        SectorName = s.SectorName,
                                        PlaneCode = s.PlaneCode,
                                        PlaneName = planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode).PlaneName,
                                        BuildingCode = planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode).BuildingCode,
                                        BuildingName = buildingList.FirstOrDefault(b => b.BuildingCode == planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode).BuildingCode).BuildingName
                                    }).ToList(),
                         IsUsageDept = (a.IsUsageDept != null && a.IsUsageDept == true) ? "Y" : "N",
                         IsManagedDept = (a.IsManagedDept != null && a.IsManagedDept == true) ? "Y" : "N",
                         Supervisor = (temp2 == null) ? "" : String.Join(", ", temp2.Select(t => t.UserName)),
                         c.AreaName,
                         a.AreaCode,
                         a.DeptEmail,
                         a.ModifyDate,
                     })
                      .Where(x => (param.DeptName == null || x.DeptName.ToUpper().Contains(param.DeptName.ToUpper()))
                               && (param.DeptCode == null || x.DeptCode.ToUpper().Contains(param.DeptCode.ToUpper()))
                               && (param.DeptCodeList == null || deptCodeList.Contains(x.DeptCode))
                               && (param.SectorCode == null || x.Sectors.Any(s => s.SectorCode.ToUpper().Contains(param.SectorCode.ToUpper())))
                               && (param.SectorName == null || x.Sectors.Any(s => s.SectorName.ToUpper().Contains(param.SectorName.ToUpper())))
                               && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                               && (param.BuildingCode == null || x.Sectors.Any(s => s.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())))
                               && (param.BuildingName == null || x.Sectors.Any(s => s.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper())))
                               && (param.PlaneCode == null || x.Sectors.Any(s => s.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper())))
                               && (param.PlaneName == null || x.Sectors.Any(s => s.PlaneName.ToUpper().Contains(param.PlaneName.ToUpper())))
                               && (param.IsManagedDept == null || x.IsManagedDept == param.IsManagedDept)
                               && (param.IsUsageDept == null || x.IsUsageDept == param.IsUsageDept)
                               && (param.Enable == null || x.Enable == param.Enable)
                          );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢


        ReturnModel returnModel = new(StatusCodes.Status200OK, true, new
        {
            recordTotal,
            recordList
        });

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
