{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Warning", "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning", "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning", "Microsoft.EntityFrameworkCore.Database": "Warning"}}, "ConnectionStrings": {"FusionS3HMNConnection": "server=postgres;port=5432;database=FusionS3HMN;uid=postgres;pwd=*************;"}, "FusionNetParam": {"ApiUrl": "http://fusion-api:8080", "ApiVersion": "v3"}, "MQTTParam": {"HostIp": "mqtt-broker", "HostPort": "1883", "UseTLS": "N", "Enable": "Y", "Timeout": 5000}, "MQTTServerParam": {"HostPort": "8888", "ConnectionBacklog": 100, "MaxPendingMessagesPerClient": 1000}, "Cors": {"AllowOrigin": "*"}, "AppInfo": {"AppCode": "hmn2", "RequestIdHeaderName": "X-Request-ID"}, "MapInfo": {"SectorUrl": "/FileStorage/sector/", "PlaneUrl": ""}, "CameraEtl": {"Enable": "Y", "WorkingDirectory": "Etls", "WorkingFileName": "fnc-application-hmn-camera-etl.jar"}}