﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;


namespace Web.Models.Controller.Department;

public class RetrieveDepartment
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string DeptCode { get; set; }
    public string DeptCodeList { get; set; }
    public string DeptName { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string PlaneCode { get; set; }
    public string PlaneName { get; set; }
    public string SectorCode { get; set; }
    public string SectorName { get; set; }
    public string IsManagedDept { get; set; }
    public string IsUsageDept { get; set; }
    public string Enable { get; set; }

    // public List<Sectors> SectorList { get; set; }
}

public class CreateDepartment
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Unique("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.Unique + "DeptCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeptCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "DeptCode")]
    public string DeptCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "DeptName")]
    public string DeptName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IsManagedDept")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsManagedDept")]
    public string IsManagedDept { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IsUsageDept")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsUsageDept")]
    public string IsUsageDept { get; set; }


    [Exists("AreaCode", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [ListAllExists("AreaCode", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.NotFound + "SectorCode")]
    public List<String> SectorCodes { get; set; }
}

public class UpdateDepartment
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    [HasReferenceWhenEquals("Enable", "N", "AreaCode", "UserDatum,Device,Device,ObjectDatum,UserDeptMonPerm", "DeptCode,UsageDepartCode,ManageDepartCode,UsageDepartCode,UsageDeptCode", ErrorMessage = Constants.ErrorCode.Reference + "DeptCode")]
    public string DeptCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "DeptName")]
    public string DeptName { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsManagedDept")]
    public string IsManagedDept { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsUsageDept")]
    public string IsUsageDept { get; set; }

    [Exists("AreaCode", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [ListAllExists("AreaCode", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.NotFound + "SectorCode")]
    public List<String> SectorCodes { get; set; }
}

public class DeleteDepartment
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    [HasReference("AreaCode", "UserDatum,Device,Device,ObjectDatum,UserDeptMonPerm", "DeptCode,UsageDepartCode,ManageDepartCode,UsageDepartCode,UsageDeptCode", ErrorMessage = Constants.ErrorCode.Reference + "DeptCode")]
    public string DeptCode { get; set; }
}


