﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Text.Json;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Task;
using Web.Models.Service.Monitor;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;
using RetrieveTask = Web.Models.Controller.Task.RetrieveTask;

namespace Web.Controller;

/// <summary>
/// 事件歷史紀錄控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class TaskController(IDataAccessService dataAccessService,
                            IMonitorService monitorService,
                            IConfigurationService configurationService,
                            IPreferenceService preferenceService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IMonitorService _monitorService = monitorService;
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IPreferenceService _preferenceService = preferenceService;

    [HttpGet("tasks")]
    public async Task<IActionResult> RetrieveTask(RetrieveTask param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.tasks.Task.param");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.tasks.Task.param"));
        }

        if (string.IsNullOrEmpty(param.AreaCode))
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.tasks.Task.AreaCode");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.tasks.Task.AreaCode"));
        }

        // 如果沒有傳入Action，或是Action為30(已結案)，則需傳入StartDate和EndDate
        if (string.IsNullOrEmpty(param.TaskAction) || param.TaskAction == "30")
        {
            if (string.IsNullOrEmpty(param.StartDate) || string.IsNullOrEmpty(param.EndDate))
            {
                _logService.Logging("error", logActionName, requestUUID, $"err.invalid.tasks.Task.Action:${param.TaskAction}");
                _logService.Logging("error", logActionName, requestUUID, $"err.null.tasks.Task.StartDate:${param.StartDate}");
                _logService.Logging("error", logActionName, requestUUID, $"err.null.tasks.Task.EndDate:${param.EndDate}");

                return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.invalid.tasks.Task.Action"));
            }

            param.TaskAction ??= "10,20,30";
        }

        var taskParam = new InTaskList
        {
            AppCode = _user.AppCode,
            ServiceCode = param.ServiceCode,
            TaskAction = param.TaskAction,
            AreaCode = param.AreaCode,
            BuildingCode = param.BuildingCode,
            PlaneCode = param.PlaneCode,
            LocCode = param.LocCode,
            DeptCode = param.DeptCode,
            ObjectCode = param.ObjectCode,
            ObjectGroup = param.ObjectGroup,
            ObjectName = param.ObjectName,
            ObjectType = param.ObjectType,
            DevicePid = param.DevicePid,
            DeviceName = param.DeviceName,
            DeviceType = param.DeviceType,
            StartDate = param.StartDate,
            EndDate = param.EndDate
        };

        var taskList = await _monitorService.GetTaskList(taskParam);

        ReturnModel returnModel = new(StatusCodes.Status200OK, true, taskList);

        _logService.Logging("info", logActionName, requestUUID, taskList.Count.ToString());
        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnModel);
    }

    [HttpGet("cannedMessages")]
    public async Task<IActionResult> RetrieveCannedMessages()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var cannedMessageList = await _dataAccessService.Fetch<CannedMessage>(e => e.AppCode == _user.AppCode && e.Enable == true).ToListAsync();

        ReturnModel returnModel = new(StatusCodes.Status200OK, true, cannedMessageList);

        _logService.Logging("info", logActionName, requestUUID, cannedMessageList.Count.ToString());
        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnModel);
    }

    [HttpGet("followingTasks")]
    public async Task<IActionResult> RetrieveFollowingTask(InGetFollowTask param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var followTaskList = await _monitorService.GetFollowingTaskList(new InFollowingTaskList
        {
            AppCode = _user.AppCode,
            AreaCode = param.AreaCode,
            DeptCodeList = _user.DeptCodeList
        });

        ReturnModel returnModel = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = followTaskList
        };

        _logService.Logging("info", logActionName, requestUUID, followTaskList.Count.ToString());
        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("history")]
    public async Task<IActionResult> GetTaskHistory([FromQuery] GetTaskHistory param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "param is null or type error");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "param is null or type error"));
        }

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        if (!pageParseResult || !sizeParseResult)
        {
            returnModel = new ReturnModel
            {
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                title = "page or size parse error"
            };
            _logService.Logging("error", logActionName, requestUUID, $"page or size parse error,page=${param.page},size=${param.size}");
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        var isAdmin = _user.IsAdmin;
        var appCode = _user.AppCode;

        var errorDetailList = new List<ErrorDetail>();

        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.history.Task.param");
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.history.Task.param" });

            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }


        if (string.IsNullOrEmpty(param.AreaCode))
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.history.Task.AreaCode");
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.history.Task.AreaCode" });
        }

        if (string.IsNullOrEmpty(param.DeptCode))
        {
            param.DeptCode = string.Join(",", _user.DeptCodeList);
        }

        if (param.StartDate != null && !DateTime.TryParseExact(param.StartDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out DateTime startDate))
        {
            _logService.Logging("error", logActionName, requestUUID, $"err.invalid.history.Task.StartDate,StartDate=${param.StartDate}");
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.history.Task.StartDate" });
        }

        if (param.EndDate != null && !DateTime.TryParseExact(param.EndDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out DateTime endDate))
        {
            _logService.Logging("error", logActionName, requestUUID, $"err.invalid.history.Task.EndDate,EndDate=${param.EndDate}");
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.history.Task.EndDate" });
        }

        // 驗證如果有錯誤，回傳錯誤訊息
        if (errorDetailList.Count != 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(errorDetailList));
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        // 對象列表
        var objectList = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && e.AreaCode == param.AreaCode).ToListAsync();

        // 裝置類型
        var deviceTypeList = await _configurationService.FetchSupportDeviceTypeList(appCode, [param.AreaCode]);

        // 支援的裝置類型代碼
        var deviceTypeCodeList = deviceTypeList.Select(d => d.type).ToList<string>();

        var serviceList = await _configurationService.GetServiceList();

        var tasks = _dataAccessService.Fetch<TaskDatum>(t => t.AppCode == appCode && t.EventCode.Contains($"@{appCode}@"));

        var taskDataList = await (from a in tasks
                                  where a.Active == true
                                     && (param.ServiceCode == null || a.ServiceCode == param.ServiceCode || ((a.ServiceCode == "Help" || a.ServiceCode == "LongPress") && param.ServiceCode == "Help"))
                                     && (param.Action == null || a.Action == Convert.ToInt32(param.Action))
                                     && (param.AreaCode == null || (a.AreaCode != null && param.AreaCode.ToUpper().Contains(a.AreaCode.ToUpper())) || (a.AreaCode == null))
                                     && (param.BuildingCode == null || (a.BuildingCode != null && a.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())))
                                     && (param.BuildingName == null || (a.BuildingName != null && a.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper())))
                                     && (param.PlaneCode == null || (a.PlaneCode != null && a.PlaneCode.ToUpper().Contains(param.PlaneCode)))
                                     && (param.PlaneName == null || (a.PlaneName != null && a.PlaneName.ToUpper().Contains(param.PlaneName.ToUpper())))
                                     && (param.SponsorLocationCode == null || (a.SponsorLocationCode != null && a.SponsorLocationCode.ToUpper() == param.SponsorLocationCode.ToUpper()))
                                     && (param.SponsorLocationName == null || (a.SponsorLocationName != null && a.SponsorLocationName.ToUpper().Contains(param.SponsorLocationName.ToUpper())))
                                     && (param.DeptCode == null || (a.DeptCode != null && param.DeptCode.ToUpper().Contains(a.DeptCode.ToUpper())) || (a.DeptCode == null))
                                     && (param.DeptName == null || (a.DeptName != null && a.DeptName.ToUpper().Contains(param.DeptName.ToUpper())))
                                     && (param.SponsorDevicePid == null || (a.SponsorDevicePid != null && a.SponsorDevicePid.ToUpper().Contains(param.SponsorDevicePid.ToUpper())))
                                     && (param.SponsorDeviceType == null || (a.SponsorDeviceType != null && a.SponsorDeviceType.ToUpper().Contains(param.SponsorDeviceType.ToUpper())))
                                     && (param.SponsorDeviceName == null || (a.SponsorDeviceName != null && a.SponsorDeviceName.ToUpper().Contains(param.SponsorDeviceName.ToUpper())))
                                     && (param.SponsorObjectCode == null || (a.SponsorObjectCode != null && a.SponsorObjectCode.ToUpper().Contains(param.SponsorObjectCode.ToUpper())))
                                     && (param.SponsorObjectName == null || (a.SponsorObjectName != null && a.SponsorObjectName.ToUpper().Contains(param.SponsorObjectName.ToUpper())))
                                     && (param.SponsorObjectType == null || (a.SponsorObjectType != null && a.SponsorObjectType.ToUpper().Contains(param.SponsorObjectType.ToUpper())))
                                     && (param.StartDate == null || a.StartsAt >= DateTime.ParseExact(param.StartDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal))
                                     && (param.EndDate == null || a.StartsAt <= DateTime.ParseExact(param.EndDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal))
                                     && (param.TaskId == null || a.TaskId == Convert.ToInt32(param.TaskId))
                                  select a).ToListAsync();

        var cannedMessageList = _dataAccessService.Fetch<CannedMessage>(e => e.AppCode == appCode);

        var eventCannedMessageList = await _dataAccessService.Fetch<EventCannedMessage>(e => e.AppCode == appCode)
                                                             .Select(e => new
                                                             {
                                                                 e.Id,
                                                                 CannedType = cannedMessageList.Where(c => c.CannedCode == e.CannedCode).FirstOrDefault().CannedType,
                                                                 e.CannedCode,
                                                                 e.TaskId
                                                             }).ToListAsync();

        // 查詢，為了Debug 方便，所以將Where條件分開
        var queryResult = from a in taskDataList
                          join d in serviceList on a.ServiceCode equals d.code into serviceJoin
                          from sj in serviceJoin.DefaultIfEmpty()
                          join e in objectList on a.SponsorObjectCode equals e.ObjectCode into objectJoin
                          from oj in objectJoin.DefaultIfEmpty()
                          orderby a.StartsAt descending
                          select new
                          {
                              a.Id,
                              a.TaskId,
                              a.EventCode,
                              a.EventName,
                              a.Action,
                              a.Active,
                              a.AreaCode,
                              a.BuildingCode,
                              a.BuildingName,
                              a.DeptCode,
                              a.DeptName,
                              a.FinishesAt,
                              GroupCode = oj?.GroupCode ?? "",
                              a.PlaneCode,
                              a.PlaneName,
                              ServiceCode = a.ServiceCode == "LongPress" ? "Help" : a.ServiceCode,
                              ServiceLabel = sj?.langs?.nameId == null ? (a.ServiceCode == "LongPress" ? "Help" : a.ServiceCode) : (a.ServiceCode == "LongPress" ? "$D00030" : $"${sj.langs.nameId}"),
                              EventCannedMessages = eventCannedMessageList.Where(ec => ec.TaskId == a.TaskId).ToList(),
                              a.SponsorDeviceName,
                              a.SponsorDevicePid,
                              a.SponsorDeviceType,
                              a.SponsorLocationCode,
                              a.SponsorLocationName,
                              a.SponsorObjectCode,
                              a.SponsorObjectName,
                              a.SponsorObjectType,
                              a.StartsAt,
                              a.PlaneMapPath,
                              a.MapWidth,
                              a.MapHeight,
                              a.PositionX,
                              a.PositionY,
                              a.SponsorStation,
                              a.DiffPositionX,
                              a.DiffPositionY,
                              a.TaskHappenReason,
                              a.TaskClearMethod,
                              a.TaskClearMoreDesc,
                              a.Extra
                          };

        var filterResult = from a in queryResult
                           where (param.GroupCode == null || (a.GroupCode != null && a.GroupCode.ToUpper().Contains(param.GroupCode.ToUpper())))
                           select a;

        //var filterResult = queryResult;

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = filterResult.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? filterResult.ToList() : queryList.Skip(skip).Take(size).ToList();

        //// 總筆數
        //var recordTotal = await query.CountAsync();

        //// 判斷是否分頁決定回傳資料
        //var recordList = size == 0 ? await query.ToListAsync() : await query.Skip(skip).Take(size).ToListAsync();

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);



    }

    [HttpPatch("resolveTasks")]
    [RequestParamListDuplicate("TaskId")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ResolveTasks([FromBody] List<ResolveTask> paramList, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _logService.Logging("info", logActionName, requestUUID, "Start Call ClearFusionTask");

        // 解除 Fusion 事件
        foreach (ResolveTask param in paramList)
        {
            var fusionResult = await _monitorService.ClearFusionTask(param.TaskId, param.TaskClearMoreDesc);

            // Fusion 事件解除失敗
            if (!fusionResult.Any(x => x.errors == null))
            {
                _logService.Logging("error", logActionName, requestUUID, Constants.ErrorCode.FusionError);

                List<ErrorDetail> errorDetailList = new List<ErrorDetail>();
                errorDetailList.Add(new ErrorDetail { index = 1, error = Constants.ErrorCode.FusionError });

                return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
            }

            _logService.Logging("info", logActionName, requestUUID, $"End Call ClearFusionTask Success,fusionResult:{JsonSerializer.Serialize(fusionResult)}");
        }

        _dataAccessService.BeginTransaction();

        int updateTaskDataCount = 0;
        foreach (ResolveTask param in paramList)
        {
            // 取得要新增的 cannedMessage
            string cannedCodes = (param.TaskHappenReason ?? "") + "," + (param.TaskClearMethod ?? "");

            // 刪除此Task 原有 cannedMessage
            int deleteCount = await _dataAccessService.DeleteAsync<EventCannedMessage>(e => e.TaskId == param.TaskId);

            _logService.Logging("info", logActionName, requestUUID, $"Delete EventCannedMessage:{deleteCount}");

            if (cannedCodes != ",")
            {
                foreach (string cannedCode in cannedCodes.Split(","))
                {
                    if (!string.IsNullOrEmpty(cannedCode))
                    {
                        var cannedMessage = new EventCannedMessage
                        {
                            AppCode = _user.AppCode,
                            TaskId = param.TaskId,
                            CannedCode = cannedCode,
                            CreateUserAccount = _user.Account,
                            CreateDate = DateTime.Now,
                            ModifyDate = DateTime.Now
                        };

                        // 新增 cannedMessage
                        await _dataAccessService.CreateAsync(cannedMessage);

                        _logService.Logging("info", logActionName, requestUUID, $"Create EventCannedMessage:{JsonSerializer.Serialize(cannedMessage)}");
                    }
                }
            }

            _logService.Logging("info", logActionName, requestUUID, "Start update task in transaction");

            // 取得事件資料
            // Ann 決議：不需要判斷Action是否為10，就算是30也可以解除 @********
            var taskDataList = await _dataAccessService.Fetch<TaskDatum>(e => e.AppCode == _user.AppCode && e.TaskId == param.TaskId).AsTracking().ToListAsync();

            foreach (var task in taskDataList)
            {
                // HMN2 不用更新 TaskHappenReason, TaskClearMethod by Ann @********
                //task.TaskHappenReason = param.TaskHappenReason;
                //task.TaskClearMethod = param.TaskClearMethod;
                task.TaskClearMoreDesc = param.TaskClearMoreDesc;

                // Action=10更改為30 時FinishesAt 才Update 系統時間
                if (task.Action == 10)
                {
                    task.FinishesAt = DateTime.Now;
                }
                task.Action = 30;
                task.ModifiesAt = DateTime.Now;

                // 更新TaskData
                await _dataAccessService.UpdateAsync(task,
                                                    callMethodName: callerName,
                                                    e => e.TaskClearMoreDesc,
                                                    e => e.Action,
                                                    e => e.FinishesAt,
                                                    e => e.ModifiesAt);

                _logService.Logging("info", logActionName, requestUUID, $"Update TaskData:{JsonSerializer.Serialize(task)}");

                updateTaskDataCount++;
            }
        }

        int updateCount = 0;
        try
        {
            updateCount = await _dataAccessService.CommitAsync();
        }
        catch (Exception ex)
        {
            _logService.Logging("error", logActionName, requestUUID, $"err.invalid.taskResolved.Task.Update:{ex.Message}");

            return BadRequest(new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                title = "err.invalid.taskResolved.Task.Update",
                innerMsg = ex.Message
            });
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskDataList.Count:{updateTaskDataCount}, Update Data:{updateCount}");

        bool updateResult = updateCount > 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = updateResult
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("resolveTask")]
    [RequestParamNotNullOrEmpty]
    public async Task<IActionResult> ResolveTask([FromBody] ResolveTask param, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var errorDetailList = new List<ErrorDetail>();

        var cannedMessageList = _dataAccessService.Fetch<CannedMessage>(e => e.AppCode == _user.AppCode).ToList();

        // 參數為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.taskResolved.Task.param");
            errorDetailList.Add(new ErrorDetail { index = 1, error = "err.null.taskResolved.Task.param" });

            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        // 事件ID不存在
        if (param.TaskId <= 0)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.taskResolved.Task.param.id");
            errorDetailList.Add(new ErrorDetail { index = 1, error = "err.null.taskResolved.Task.param.id" });

            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskId={param.TaskId}");

        // 取得事件資料
        // Ann 決議：不需要判斷Action是否為10，就算是30也可以解除 @********
        var taskDataList = await _dataAccessService.Fetch<TaskDatum>(e => e.AppCode == _user.AppCode && e.TaskId == param.TaskId).AsTracking().ToListAsync();

        // 事件不存在
        if (taskDataList == null || taskDataList.Count == 0)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.notFound.taskResolved.Task.param.id");
            errorDetailList.Add(new ErrorDetail { index = 1, error = "err.notFound.taskResolved.Task.param.id" });

            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        _logService.Logging("info", logActionName, requestUUID, $"Task:{JsonSerializer.Serialize(taskDataList)}");

        // 發生原因、處理方式、處理說明皆為空
        if (string.IsNullOrEmpty(param.TaskHappenReason) && string.IsNullOrEmpty(param.TaskClearMoreDesc) && string.IsNullOrEmpty(param.TaskClearMethod))
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.taskResolved.Task.param.HappenReason");
            errorDetailList.Add(new ErrorDetail { index = 1, error = "err.null.taskResolved.Task.param.ClearMethod" });
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskHappenReason={param.TaskHappenReason},TaskClearMethod={param.TaskClearMethod},TaskClearMoreDesc={param.TaskClearMoreDesc}");

        // 檢查TaskHappenReason是否存在
        if (!string.IsNullOrEmpty(param.TaskHappenReason))
        {
            foreach (string reason in param.TaskHappenReason.Split(","))
            {
                if (!cannedMessageList.Any(x => x.CannedCode == reason))
                {
                    _logService.Logging("error", logActionName, requestUUID, "err.exists.taskResolved.Task.param.HappenReason");
                    errorDetailList.Add(new ErrorDetail { index = 1, error = "err.exists.taskResolved.Task.param.HappenReason" });
                }
            }
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskHappenReason={param.TaskHappenReason}");

        // 檢查TaskClearMethod是否存在
        if (!string.IsNullOrEmpty(param.TaskClearMethod))
        {
            foreach (string method in param.TaskClearMethod.Split(","))
            {
                if (!cannedMessageList.Any(x => x.CannedCode == method))
                {
                    errorDetailList.Add(new ErrorDetail { index = 1, error = "err.exists.taskResolved.Task.param.ClearMethod" });
                }
            }
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskClearMethod={param.TaskClearMethod}");

        // 驗證如果有錯誤，回傳錯誤訊息
        if (errorDetailList != null && errorDetailList.Count > 0)
        {
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        _logService.Logging("info", logActionName, requestUUID, "Start Call ClearFusionTask");

        // 解除 Fusion 事件
        var fusionResult = await _monitorService.ClearFusionTask(taskDataList.FirstOrDefault()?.TaskId ?? 0, param.TaskClearMoreDesc);

        // Fusion 事件解除失敗
        if (!fusionResult.Any(x => x.errors == null))
        {
            _logService.Logging("error", logActionName, requestUUID, "err.invalid.taskResolved.Monitor.FusionResult");
            errorDetailList.Add(new ErrorDetail { index = 1, error = "err.invalid.taskResolved.Monitor.FusionResult" });

            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        //_logService.Logging("info", logActionName, requestUUID, $"End Call ClearFusionTask Success,fusionResult:{JsonConverter}");
        _logService.Logging("info", logActionName, requestUUID, $"End Call ClearFusionTask Success,fusionResult:{JsonSerializer.Serialize(fusionResult)}");


        // 取得要新增的 cannedMessage
        string cannedCodes = (param.TaskHappenReason ?? "") + "," + (param.TaskClearMethod ?? "");

        _dataAccessService.BeginTransaction();

        // 刪除此Task 原有 cannedMessage
        int deleteCount = await _dataAccessService.DeleteAsync<EventCannedMessage>(e => e.TaskId == param.TaskId);

        _logService.Logging("info", logActionName, requestUUID, $"Delete EventCannedMessage:{deleteCount}");

        if (cannedCodes != ",")
        {
            foreach (string cannedCode in cannedCodes.Split(","))
            {
                if (!string.IsNullOrEmpty(cannedCode))
                {
                    var cannedMessage = new EventCannedMessage
                    {
                        AppCode = _user.AppCode,
                        TaskId = param.TaskId,
                        CannedCode = cannedCode,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now,
                        ModifyDate = DateTime.Now
                    };

                    // 新增 cannedMessage
                    await _dataAccessService.CreateAsync(cannedMessage);

                    _logService.Logging("info", logActionName, requestUUID, $"Create EventCannedMessage:{JsonSerializer.Serialize(cannedMessage)}");
                }
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "Start update task in transaction");

        foreach (var task in taskDataList)
        {
            // HMN2 不用更新 TaskHappenReason, TaskClearMethod by Ann @********
            //task.TaskHappenReason = param.TaskHappenReason;
            //task.TaskClearMethod = param.TaskClearMethod;
            task.TaskClearMoreDesc = param.TaskClearMoreDesc;

            // Action=10更改為30 時FinishesAt 才Update 系統時間
            if (task.Action == 10)
            {
                task.FinishesAt = DateTime.Now;
            }
            task.Action = 30;
            task.ModifiesAt = DateTime.Now;

            // 更新TaskData
            await _dataAccessService.UpdateAsync(task,
                                                callMethodName: callerName,
                                                e => e.TaskClearMoreDesc,
                                                e => e.Action,
                                                e => e.FinishesAt,
                                                e => e.ModifiesAt);

            _logService.Logging("info", logActionName, requestUUID, $"Update TaskData:{JsonSerializer.Serialize(task)}");
        }

        int updateCount = 0;
        try
        {
            updateCount = await _dataAccessService.CommitAsync();
        }
        catch (Exception ex)
        {
            _logService.Logging("error", logActionName, requestUUID, $"err.invalid.taskResolved.Task.Update:{ex.Message}");

            return BadRequest(new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                title = "err.invalid.taskResolved.Task.Update",
                innerMsg = ex.Message
            });
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskDataList.Count:{taskDataList.Count}, Update Data:{updateCount}");

        bool updateResult = updateCount > 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = updateResult
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}
