﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Location
{
    public int LocId { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public bool? Enable { get; set; }

    public string? LocCode { get; set; }

    public string? LocName { get; set; }

    public string? BuildingCode { get; set; }

    public string? PlaneCode { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public bool? Utilitzation { get; set; }
}
