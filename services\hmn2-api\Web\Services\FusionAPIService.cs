﻿using DocumentFormat.OpenXml.Math;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NLog.Targets;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Dynamic;
using System.Globalization;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Web.Converts;
using Web.Models.Controller;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using static Web.Models.Service.Fusion.DeviceResults;

namespace Web.Services.Interfaces
{
    public interface INotificationsService
    {
        public Task<SendMessageResult> SendMail(string appCode, string subject, string body, string to, string cc = null, string bcc = null, string from = null, string replyTo = null, string[] attachments = null);
        public Task<SendMessageResult> SendLineNotify(string appCode, string message, string to, string accessToken = null, string url = null);
        public Task<SendMessageResult> SendENSMessage(string appCode, string objectCode, int taskId, string taskType, string url, string message, string taskExtra, string toSid = null);
        public Task<SendMessageResult> SendDisplayMessage(string appCode, string objectCode, int taskId, string taskType, string receiverCode, string message, string message2, string taskExtra);
    }
    public interface IObjectService
    {
        public Task<List<PostAPIResult>> AddObject(List<PostFusionObjectInput> objectList);
        public Task<List<PostAPIResult>> AddObjectValidation(List<PostFusionObjectInput> objectList);
        public Task<List<PatchAPIResult>> AddDevice(List<AddObjectDeviceInput> deviceList);
        public Task<List<PatchAPIResult>> PatchObject(List<PatchFusionObjectInput> objectList);
        public Task<List<DeleteAPIResult>> DeleteObject(string objectCode);
    }

    public interface ILangService
    {
        public Task<List<serviceCode>> GetServiceCodes();
    }

    public interface ICameraService
    {
        public Task<List<GetCameraOutput>> GetCameraList(string search = null);
        public Task<List<PostAPIResult>> AddCamera(List<AddCameraInput> cameraList);
        public Task<List<PatchAPIResult>> PatchCamera(List<PatchCameraInput> cameraList);
        public Task<List<DeleteAPIResult>> DeleteCamera(string code);
    }

    public interface IDeviceService
    {
        public Task<List<Devices>> GetDeviceList(string appCode = null, string search = null);
        public Task<List<PostAPIResult>> AddDevice(List<AddDeviceInput> deviceList);
        public Task<List<PatchAPIResult>> PatchDevice(List<UpdateDeviceInput> deviceList);
        public Task<List<DeviceType>> GetDeviceTypeList();
        public Task<List<DevicePositions>> GetDevicePositionsList();
        public Task<List<DeleteAPIResult>> UnbondObject(string pid);
        public Task<List<DeleteAPIResult>> DeleteDevice(string pid);
        public Task<int> GetTrajectoryCount(TrajectoryInput param);
        public Task<List<TrajectoryOutput>> GetTrajectoryList(TrajectoryInput param);
        public Task<List<DeviceResults.Result>> GetDeviceReport(InGetDeviceReport param);
        public List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults);
        Task<List<PostAPIResult>> ValidateDevice(List<AddDeviceInput> deviceList);
    }

    public interface IEventService
    {
        public Task<List<PostAPIResult>> AddActiveEvent(List<PostFusionEventInput> _eventData);
        public Task<List<DeleteAPIResult>> DeleteActiveEvent(string eventCode);
        public Task<List<PatchAPIResult>> PatchActiveEvents(List<PatchFusionEventInput> _eventData);
        public Task<HashSet<string>> ExistEvents(List<string> eventCodeList);
    }


    public interface IStationService
    {
        public Task<List<Stations>?> GetStationListWithDevices(string stationSid);
        public Task<List<Stations>?> GetStationList(string search = null);
        Task<List<PostAPIResult>> AddStation(List<AddStationInput> stationList);
        Task<List<PostAPIResult>> AddStationValidation(List<AddStationInput> stationList);
        Task<List<PatchAPIResult>> PatchStation(List<IDictionary<string, object>> stationList);
        Task<List<PatchAPIResult>> PatchConfiguration(List<PatchConfigurationInput> stationList);
        Task<List<DeleteAPIResult>> DeleteStation(string sids);
        List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults);
        Task<List<PostAPIResult>> Command(List<StationCommandInput> commandList);
        Task<List<ConfigurationsCompatible>?> GetConfigurationsCompatible(string search = null, string resourceIds = null);
        Task<List<PostAPIResult>> Reboot(List<string> sidList);
    }

    public interface ITaskService
    {
        public Task<List<PatchAPIResult>> PatchTask(List<dynamic> taskList);
        public Task<List<FusionTask>>? GetTaskList(string appCode = null, string search = null);
    }

    public interface IPlaneService
    {
        public Task<List<Planes>?> GetPlaneList(string search = null);
        Task<List<PostAPIResult>> AddPlane(List<PostPlaneInput> planeList);
        Task<List<PostAPIResult>> AddPlaneValidation(List<PostPlaneInput> planeList);
        Task<PutAPIResult> PutPlane(PutPlaneInput planeImage);
        Task<List<PatchAPIResult>> PatchPlane(List<PatchPlaneInput> planeList);
        Task<List<DeleteAPIResult>> DeletePlane(string planeCodes);
        List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults);
    }

    public interface ILicenseService
    {
        public Task<List<GetLicenseServiceOutput>> GetServiceList(string search = null);
        public Task<List<GetLicenseNotifyConfigOutput>> GetNotifyConfigList(string search = null);
    }

    public interface ISystemService
    {
        public Task<List<GetSystemConfigurationsOutput>> GetSystemConfigurationList(string search = null);
    }
}

namespace Web.Services.Fusion
{
    public class FusionNetBase
    {
        private readonly string _appCode = "FusionNet";
        private readonly string _userAccount = "ScheduleJob";
        private readonly string _requestIP = "::1";
        protected readonly string _apiUrl = "";
        protected readonly string _apiVersion = "v3";

        private readonly IHttpContextAccessor _accessor;
        private readonly IConfiguration _configuration;
        IHttpClientFactory _httpClientFactory;
        private readonly HttpClient _httpClient;
        private readonly IDataAccessService _dataAccessService;

        protected FusionNetBase(IHttpContextAccessor accessor,
                                IConfiguration configuration,
                                IHttpClientFactory httpClientFactory,
                                IDataAccessService dataAccessService)
        {
            _accessor = accessor;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _dataAccessService = dataAccessService;

            // 讀取配置文件（appsettings.json）中的 AppCode 和 FusionNetParam:ApiUrl
            _apiUrl = _configuration["FusionNetParam:ApiUrl"] ?? throw new ConfigurationErrorsException("FusionNetParam:ApiUrlNotFoundAppCode");
            string apiVersion = _configuration["FusionNetParam:ApiVersion"] ?? throw new ConfigurationErrorsException("FusionNetParam:ApiVersionNotFoundAppCode");
            if (!string.IsNullOrEmpty(apiVersion))
            {
                _apiVersion = apiVersion;
            }

            // 取得登入者 AppCode
            string? appCode = _accessor.HttpContext?.User.Claims.FirstOrDefault(x => x.Type == "AppCode")?.Value;
            if (!string.IsNullOrEmpty(appCode))
            {
                _appCode = appCode;
                _httpClient = GetHttpClient(appCode).Result;
            }

            // 取得登入者帳號
            string? userAccount = _accessor.HttpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
            if (!string.IsNullOrEmpty(userAccount))
            {
                _userAccount = userAccount;
            }

            // 取得用戶端的IP位址
            string requestIP = string.IsNullOrEmpty(_accessor?.HttpContext?.Request.Headers["X-Forwarded-For"]) ?
                                            _accessor?.HttpContext?.Connection.RemoteIpAddress.ToString() :
                                            _accessor?.HttpContext?.Request.Headers["X-Forwarded-For"].ToString();
            if (!string.IsNullOrEmpty(requestIP))
            {
                _requestIP = requestIP;
            }
        }

        protected async Task<HttpClient> GetHttpClient(string appCode)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient("FusionCoreApi");

            // 依據 AppCode 從Organization 取得FusionNet的LicenseKey和LicenseCode
            var organizations = await _dataAccessService.FetchWithNewContext<Organization>(e => e.AppCode == appCode);
            var organization = organizations.FirstOrDefault() ?? throw new InvalidOperationException("Organization not found match AppCode");

            string licenseKey = organization.LicenseKey ?? throw new InvalidOperationException("licenseKeyNotFound");
            string licenseCode = organization.LicenseCode ?? throw new InvalidOperationException("licenseCodeNotFound");

            // 計算簽名
            string dateNow = DateTime.UtcNow.ToString("r");
            using var hmac = new HMACSHA1(Encoding.UTF8.GetBytes(licenseKey));
            string sha1 = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes("x-date: " + dateNow)));
            var _signature = $"hmac username=\"{licenseCode}\",algorithm=\"hmac-sha1\",headers=\"x-date\",signature=\"{sha1}\"";

            // 設置 HttpClient 的基地址和預設請求標頭
            httpClient.BaseAddress = new Uri(_apiUrl);
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation("x-api-key", licenseKey);
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _signature);
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation("x-date", dateNow);

            return httpClient;
        }

        private async Task WriteLog(string apiUrl, string requestMethod, string requestParam, string responseResult,
                                    int connTiming, int execTiming, int statusCode = 9999, string reasonPhrase = "")
        {
            // 先查詢 SysParameters 作為基礎數據
            var sysParameters = await _dataAccessService.FetchWithNewContext<SysParameters>(x => x.ParaCode == "ConsoleApiLog");
            var sysParameter = sysParameters.FirstOrDefault();

            // 查詢 GlobalSysPara 用於覆蓋
            var globalSysParas = await _dataAccessService.FetchWithNewContext<GlobalSysPara>(x => x.AppCode == _appCode && x.ParaCode == "ConsoleApiLog");
            var globalSysPara = globalSysParas.FirstOrDefault();

            // 決定最終使用的 ParaValue，並確保非空
            string paraValue = globalSysPara?.ParaValue ?? sysParameter?.ParaValue ?? "";

            // 如果 paraValue 為 null 或空，則提前返回
            if (string.IsNullOrEmpty(paraValue)) return;
            // 讀取系統參數的值，判斷是否要寫log
            var consoleApiLogParaModel = JsonSerializer.Deserialize<ConsoleApiLogParaModel>(paraValue);
            // 如果 consoleApiLogParaModel 或其 enabled 為 null，則提前返回
            if (consoleApiLogParaModel == null || consoleApiLogParaModel?.enabled == null) return;
            // 如果 consoleApiLogParaModel 的 enabled 為 false，則提前返回
            if (consoleApiLogParaModel?.enabled != true) return;
            // 如果 consoleApiLogParaModel 的 enabled 為 true，則要再判斷是否有指定 clientIP
            if (consoleApiLogParaModel?.enabled == true)
            {
                // 如果 clientIP 不為 null 或空白，則要再判斷是否等於 requestIP
                if (consoleApiLogParaModel?.clientIP != null || consoleApiLogParaModel?.clientIP.Trim() != "")
                {
                    if (consoleApiLogParaModel?.clientIP == "::0" || consoleApiLogParaModel?.clientIP == "0.0.0.0")
                    {
                        // 代表不判斷Client IP，所以全部存取都要寫log
                    }
                    // 如果有指定要判斷的ClientIP
                    else
                    {
                        // 如果 requestIP 為 null 或空白，則提前返回（代表抓不到用戶端的IP位址）
                        if (_requestIP == null || _requestIP.Trim() == "") return;
                        // 如果 clientIP 不等於 requestIP，則提前返回
                        if (consoleApiLogParaModel?.clientIP != _requestIP) return;
                    }
                }
                // 如果 clientIP 為 null 或空白
                else
                {
                    // 代表沒有指定要判斷的ClientIP，所以全部存取都要寫log
                }
            }

            var log = new FusionLog
            {
                ApiUrl = apiUrl,
                AppCode = _appCode,
                ClientIP = _requestIP,
                ConnTiming = connTiming,
                ExecTiming = execTiming,
                ExecResult = statusCode == 200 || statusCode == 9999 ? "Success" : "Fail",
                RequestMethod = requestMethod,
                RequestParam = requestParam,
                ResponseResult = responseResult,
                ReasonPhrase = reasonPhrase,
                StatusCode = statusCode,
                CreateUserAccount = _userAccount,
                CreateDate = DateTime.Now
            };

            await _dataAccessService.CreateAsync(log);
        }

        #region GetFusionNetCore 取得FusionNet資料
        protected async Task<JsonResult> GetFusionNetCore(string api)
        {
            Stopwatch sw = new();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            HttpResponseMessage objectresponse = await _httpClient.GetAsync(api);
            var objectcontent = await objectresponse.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(_httpClient.BaseAddress + api, "GET", "", objectcontent, connTiming, execTiming);

            return new JsonResult(result);

        }
        #endregion

        #region GetFusionNetCore 取得FusionNet資料
        protected async Task<JsonResult> GetFusionNetCore(string appCode, string api)
        {
            Stopwatch sw = new();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            var httpClient = string.IsNullOrEmpty(appCode) ? _httpClient : await GetHttpClient(appCode);
            HttpResponseMessage objectresponse = await httpClient.GetAsync(api);
            var objectcontent = await objectresponse.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(httpClient.BaseAddress + api, "GET", "", objectcontent, connTiming, execTiming);

            return new JsonResult(result);

        }
        #endregion

        #region PatchFusionNetCore 更新FusionNet資料
        protected async Task<JsonResult> PatchFusionNetCore(string api, dynamic toPatchData)
        {
            Stopwatch sw = new Stopwatch();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            var content = new StringContent(JsonSerializer.Serialize(toPatchData, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull }), Encoding.UTF8, "application/json-patch+json");

            HttpResponseMessage objectresponse = await _httpClient.PatchAsync(api, content);
            var objectcontent = await objectresponse.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(_httpClient.BaseAddress + api, "Patch", JsonSerializer.Serialize(toPatchData), objectcontent, connTiming, execTiming);

            return new JsonResult(result);
        }
        #endregion

        #region PostFusionNetCore 新增FusionNet資料
        protected async Task<(int, JsonResult)> PostFusionNetCore(string api, dynamic postData)
        {
            Stopwatch sw = new Stopwatch();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            var jsonPostData = JsonSerializer.Serialize(postData, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull });
            var content = new StringContent(jsonPostData, Encoding.UTF8, "application/json-post+json");

            string curlCommand = GenerateCurlCommand(api, content);

            HttpResponseMessage response = await _httpClient.PostAsync(api, content).ConfigureAwait(false);
            int responseHttpStatusCode = (int)response.StatusCode;
            string responseHttpReasonPhrase = response.ReasonPhrase;
            string objectcontent = await response.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(_httpClient.BaseAddress + api, "POST", JsonSerializer.Serialize(postData, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull }), objectcontent, connTiming, execTiming, responseHttpStatusCode, responseHttpReasonPhrase);

            return (responseHttpStatusCode, new JsonResult(result));
        }
        #endregion

        #region PostFusionNetCore 新增FusionNet資料
        protected async Task<(int, JsonResult)> PostFusionNetCore(string appCode, string api, dynamic postData)
        {
            Stopwatch sw = new Stopwatch();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            var jsonPostData = JsonSerializer.Serialize(postData, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull });
            var content = new StringContent(jsonPostData, Encoding.UTF8, "application/json-post+json");

            var httpClient = string.IsNullOrEmpty(appCode) ? _httpClient : await GetHttpClient(appCode);
            string curlCommand = GenerateCurlCommand(httpClient, api, content);

            HttpResponseMessage response = await httpClient.PostAsync(api, content).ConfigureAwait(false);
            int responseHttpStatusCode = (int)response.StatusCode;
            string responseHttpReasonPhrase = response.ReasonPhrase;
            string objectcontent = await response.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(httpClient.BaseAddress + api, "POST", JsonSerializer.Serialize(postData, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull }), objectcontent, connTiming, execTiming, responseHttpStatusCode, responseHttpReasonPhrase);

            return (responseHttpStatusCode, new JsonResult(result));
        }
        #endregion

        #region DeleteFusionNetCore 刪除FusionNet資料
        protected async Task<JsonResult> DeleteFusionNetCore(string api)
        {
            Stopwatch sw = new Stopwatch();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();
            HttpResponseMessage objectresponse = await _httpClient.DeleteAsync(api);
            var objectcontent = await objectresponse.Content.ReadAsStringAsync();
            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(_httpClient.BaseAddress + api, "DELETE", "", objectcontent, connTiming, execTiming);

            return new JsonResult(objectcontent);
        }
        #endregion

        #region PutFusionNetCore put FusionNet資料
        protected async Task<(int, JsonResult)> PutFusionNetCoreWithMultipart(string api, MultipartFormDataContent multipartData)
        {
            Stopwatch sw = new Stopwatch();

            int connTiming = 0; int execTiming = 0;

            sw.Restart();

            HttpResponseMessage objectresponse = await _httpClient.PutAsync(api, multipartData);
            int responseHttpStatusCode = (int)objectresponse.StatusCode;
            var objectcontent = await objectresponse.Content.ReadAsStringAsync();

            sw.Stop();

            execTiming = (int)sw.ElapsedMilliseconds;

            var result = JsonSerializer.Deserialize<dynamic>(string.IsNullOrWhiteSpace(objectcontent) ? "{}" : objectcontent);

            await WriteLog(_httpClient.BaseAddress + api, "PUT", "", objectcontent, connTiming, execTiming);

            return (responseHttpStatusCode, new JsonResult(result));
        }
        #endregion

        private string GenerateCurlCommand(string url, StringContent content)
        {
            var curlBuilder = new StringBuilder();
            curlBuilder.Append($"curl -X POST \"{_httpClient.BaseAddress}{url}\"");

            // 添加默认头信息
            foreach (var header in _httpClient.DefaultRequestHeaders)
            {
                foreach (var value in header.Value)
                {
                    curlBuilder.Append($" -H \"{header.Key}: {value}\"");
                }
            }

            // 添加内容类型头信息
            if (content.Headers.ContentType != null)
            {
                curlBuilder.Append($" -H \"Content-Type: {content.Headers.ContentType}\"");
            }

            // 添加内容
            string contentString = content.ReadAsStringAsync().Result;
            curlBuilder.Append($" -d \"{contentString}\"");

            return curlBuilder.ToString();
        }

        private string GenerateCurlCommand(HttpClient httpClient, string url, StringContent content)
        {
            var curlBuilder = new StringBuilder();
            curlBuilder.Append($"curl -X POST \"{httpClient.BaseAddress}{url}\"");

            // 添加默认头信息
            foreach (var header in httpClient.DefaultRequestHeaders)
            {
                foreach (var value in header.Value)
                {
                    curlBuilder.Append($" -H \"{header.Key}: {value}\"");
                }
            }

            // 添加内容类型头信息
            if (content.Headers.ContentType != null)
            {
                curlBuilder.Append($" -H \"Content-Type: {content.Headers.ContentType}\"");
            }

            // 添加内容
            string contentString = content.ReadAsStringAsync().Result;
            curlBuilder.Append($" -d \"{contentString}\"");

            return curlBuilder.ToString();
        }

        public List<ReturnError> GetFusionError<T>(List<T> commonAPIResults) where T : CommonAPIResult
        {
            List<ReturnError> addFusionErrors = commonAPIResults
                .AsParallel()
                .Select((result, i) => new
                {
                    Index = i,
                    Result = result
                })
                .Where(x => x.Result.errors?.Any() == true)
                .Select(x => new ReturnError
                {
                    index = x.Index,
                    code = x.Result.code ?? x.Result.pid,
                    errors = x.Result.errors
                        .Select((error, errorIndex) => new ErrorDetail
                        {
                            index = errorIndex + 1,
                            error = $"err.{error.error}.fusion",
                            code = null,
                            message = null,
                            innerMsg = error.description,
                            details = []
                        })
                        .ToList()
                })
                .ToList();

            return addFusionErrors ?? [];
        }
    }
    public class TaskServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          JsonSerializerOptions options,
                          IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), ITaskService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = options;

        public async Task<List<PatchAPIResult>> PatchTask(List<dynamic> taskList)
        {
            try
            {
                var result = await base.PatchFusionNetCore($"/events/api/{base._apiVersion}/tasks", taskList).ConfigureAwait(false);

                return JsonSerializer.Deserialize<List<PatchAPIResult>>(result.Value?.ToString() ?? "") ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<FusionTask>>? GetTaskList(string appCode, string search = null)
        {
            var responseContent = await base.GetFusionNetCore(appCode, $"/events/api/{base._apiVersion}/tasks?search={search}").ConfigureAwait(false);

            string responseContentString = JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent?.Value?.ToString() ?? "")?["results"].ToString() ?? "";

            return JsonSerializer.Deserialize<List<FusionTask>>(responseContentString, _jsonSerializerOptions) ?? [];
        }
    }
    public class NotificationsServiceNet : FusionNetBase, INotificationsService
    {
        public NotificationsServiceNet(IHttpContextAccessor accessor,
                              IConfiguration configuration,
                              IHttpClientFactory httpClientFactory,
                              IDataAccessService dataAccessService) : base(accessor, configuration, httpClientFactory, dataAccessService)
        {
        }

        public async Task<SendMessageResult> SendMail(string appCode, string subject, string body, string to, string cc = null, string bcc = null, string from = null, string replyTo = null, string[] attachments = null)
        {
            MailModel mailInfo = new MailModel()
            {
                arguments = new MailArguments() { receiverCode = to },
                content = new MailContent() { subject = subject, message = body },
            };

            var result = await base.PostFusionNetCore(appCode, $"/notifications/api/{base._apiVersion}/webhooks/messages/passthrough", mailInfo).ConfigureAwait(false);

            // 判斷 API 成功與否，以 PostFusionNetCore 的第一個回傳值是否等於 200
            return new SendMessageResult()
            {
                success = result.Item1 == 200 ? 1 : 0,
                fail = result.Item1 == 200 ? 0 : 1
            };
        }

        public async Task<SendMessageResult> SendLineNotify(string appCode, string message, string to, string accessToken = null, string url = null)
        {
            LineModel lineInfo = new LineModel()
            {
                arguments = !string.IsNullOrWhiteSpace(accessToken) && !string.IsNullOrWhiteSpace(url) ? new LineArguments() { accessToken = accessToken, url = url } : null,
                content = new LineContent() { message = message, to = to },
            };

            var result = await base.PostFusionNetCore(appCode, $"/notifications/api/{base._apiVersion}/webhooks/messages/passthrough", lineInfo).ConfigureAwait(false);

            // 判斷 API 成功與否，以 PostFusionNetCore 的第一個回傳值是否等於 200
            return new SendMessageResult()
            {
                success = result.Item1 == 200 ? 1 : 0,
                fail = result.Item1 == 200 ? 0 : 1
            };
        }

        public async Task<SendMessageResult> SendENSMessage(string appCode, string objectCode, int taskId, string taskType, string url, string message, string taskExtra, string toSid = null)
        {
            ENSMessageModel ensMessageInfo = new ENSMessageModel()
            {
                arguments = new ENSMessageArguments() { url = url },
                content = new ENSMessageContent()
                {
                    message = message,
                    objectCode = objectCode,
                    taskId = taskId,
                    taskType = taskType,
                    taskExtra = JsonSerializer.Deserialize<extra>(taskExtra),
                    position = string.IsNullOrEmpty(toSid) ? null : new Web.Models.Service.Fusion.Position() { toSid = toSid }
                },
            };

            var result = await base.PostFusionNetCore(appCode, $"/notifications/api/{base._apiVersion}/webhooks/messages/passthrough", ensMessageInfo).ConfigureAwait(false);

            // 判斷 API 成功與否，以 PostFusionNetCore 的第一個回傳值是否等於 200
            return new SendMessageResult()
            {
                success = result.Item1 == 200 ? 1 : 0,
                fail = result.Item1 == 200 ? 0 : 1
            };
        }

        public async Task<SendMessageResult> SendDisplayMessage(string appCode, string objectCode, int taskId, string taskType, string receiverCode, string message, string message2, string taskExtra)
        {
            DisplayMessageModel displayMessageInfo = new DisplayMessageModel()
            {
                arguments = new DisplayMessageArguments() { receiverCode = receiverCode },
                content = new DisplayMessageContent()
                {
                    message = message,
                    message2 = message2,
                    objectCode = objectCode,
                    taskId = taskId,
                    taskType = taskType,
                    taskExtra = JsonSerializer.Deserialize<extra>(taskExtra)
                },
            };

            var result = await base.PostFusionNetCore(appCode, $"/notifications/api/{base._apiVersion}/webhooks/asyncMessages/push", displayMessageInfo).ConfigureAwait(false);

            // 判斷 API 成功與否，以 PostFusionNetCore 的第一個回傳值是否等於 200
            return new SendMessageResult()
            {
                success = result.Item1 == 200 ? 1 : 0,
                fail = result.Item1 == 200 ? 0 : 1
            };
        }
    }

    public class ObjectServiceNet : FusionNetBase, IObjectService
    {
        public ObjectServiceNet(IHttpContextAccessor accessor,
                              IConfiguration configuration,
                              IHttpClientFactory httpClientFactory,
                              IDataAccessService dataAccessService) : base(accessor, configuration, httpClientFactory, dataAccessService)
        {

        }

        public async Task<List<PostAPIResult>> AddObject(List<PostFusionObjectInput> objectList)
        {
            try
            {
                var (httpStatusCode, result) = await base.PostFusionNetCore($"/objects/api/{base._apiVersion}/objects", objectList).ConfigureAwait(false);

                return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PostAPIResult>> AddObjectValidation(List<PostFusionObjectInput> objectList)
        {
            try
            {
                var (httpStatusCode, result) = await base.PostFusionNetCore($"/objects/api/{base._apiVersion}/objects/validation", objectList).ConfigureAwait(false);

                return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PatchAPIResult>> PatchObject(List<PatchFusionObjectInput> objectList)
        {
            try
            {
                var result = await PatchFusionNetCore($"/objects/api/{base._apiVersion}/objects", objectList).ConfigureAwait(false);

                return JsonSerializer.Deserialize<List<PatchAPIResult>>(result.Value?.ToString() ?? "");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<PatchAPIResult>> AddDevice(List<AddObjectDeviceInput> deviceList)
        {
            try
            {
                var result = await base.PatchFusionNetCore($"/objects/api/{base._apiVersion}/objects", deviceList).ConfigureAwait(false);

                return JsonSerializer.Deserialize<List<PatchAPIResult>>(result.Value?.ToString() ?? "");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        //public async Task<System.Object> PatchDevice(List<ObjectDeviceInput> deviceList)
        //{
        //    try
        //    {
        //        var result = await base.PatchFusionNetCore($"/devices/api/{base._apiVersion}/devices", deviceList).ConfigureAwait(false);

        //        return result.Value;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new Exception(ex.Message);
        //    }
        //}

        public async Task<List<DeleteAPIResult>> DeleteObject(string objectCode)
        {
            var result = await base.DeleteFusionNetCore($"/objects/api/{base._apiVersion}/objects/{objectCode}/active").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result.Value?.ToString() ?? "");
        }
    }
    public class LangServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          IDataAccessService dataAccessService,
                          IMemoryCache memoryCache) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), ILangService
    {
        private readonly IMemoryCache _memoryCache = memoryCache;
        private readonly string _serviceCodesCacheKey = "FusionServiceCodes";
        private readonly SemaphoreSlim _cacheLock = new(1, 1); // 用于互斥访问的锁

        public async Task<List<serviceCode>?> GetServiceCodes()
        {
            if (_memoryCache.TryGetValue(_serviceCodesCacheKey, out List<serviceCode>? cachedServiceCodes))
            {
                return cachedServiceCodes;
            }

            // 缓存未命中，进入锁定代码块
            await _cacheLock.WaitAsync();
            try
            {
                // 再次检查是否已经被其他线程计算并放入缓存
                if (!_memoryCache.TryGetValue(_serviceCodesCacheKey, out cachedServiceCodes))
                {
                    // 如果仍然不在缓存中，执行长时间运行的获取操作
                    var responseContent = await base.GetFusionNetCore($"/langs/api/{base._apiVersion}/langs/serviceCodes").ConfigureAwait(false);
                    cachedServiceCodes = JsonSerializer.Deserialize<List<serviceCode>>(JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent?.Value?.ToString() ?? "")?["results"].ToString() ?? "");

                    // 设置内存缓存选项
                    var cacheEntryOptions = new MemoryCacheEntryOptions
                    {
                        /**
                         * CacheItemPriority 枚举有以下几个值：
                         * 
                         * Low：低优先级，在内存压力较大时会被优先移除。
                         * Normal：普通优先级，默认值。
                         * High：高优先级，在内存压力较大时会尽可能保留。
                         * NeverRemove：永不移除，除非手动删除或缓存服务被释放。
                         */
                        Priority = CacheItemPriority.NeverRemove // 通常FusionCore的資料不會變動，所以設定為永不移除，如果有變動，HMN 會重啟服務
                    };

                    _memoryCache.Set(_serviceCodesCacheKey, cachedServiceCodes, cacheEntryOptions);
                }
            }
            finally
            {
                _cacheLock.Release();
            }

            return cachedServiceCodes;
        }
    }
    public class CameraServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          JsonSerializerOptions jsonSerializerOptions,
                          IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), ICameraService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        public async Task<List<GetCameraOutput>> GetCameraList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/cameras/api/{base._apiVersion}/cameras?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<GetCameraOutput>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions) ?? [];
        }

        public async Task<List<PostAPIResult>> AddCamera(List<AddCameraInput> cameraList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/cameras/api/{base._apiVersion}/cameras", cameraList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<PatchAPIResult>> PatchCamera(List<PatchCameraInput> cameraList)
        {
            var result = await base.PatchFusionNetCore($"/cameras/api/{base._apiVersion}/cameras", cameraList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<DeleteAPIResult>> DeleteCamera(string code)
        {
            var result = await base.DeleteFusionNetCore($"/cameras/api/{base._apiVersion}/cameras/{code}/active").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }
    }
    public class DeviceServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          JsonSerializerOptions jsonSerializerOptions,
                          IDataAccessService dataAccessService,
                          IMemoryCache memoryCache) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), IDeviceService
    {
        private readonly IMemoryCache _memoryCache = memoryCache;
        private readonly string _deviceTypesCacheKey = "FusionDeviceTypes";
        private readonly SemaphoreSlim _cacheLock = new(1, 1); // 用于互斥访问的锁

        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        /**
         * 新增裝置<br/>
         * 這個是購買新裝置時做為建檔資料用，若是對象要配對裝置，則是要call ObjectServiceNet.AddDevice
         */
        public async Task<List<PostAPIResult>> AddDevice(List<AddDeviceInput> deviceList)
        {
            var (httpStatusCode, json) = await base.PostFusionNetCore($"/devices/api/{base._apiVersion}/devices", deviceList).ConfigureAwait(false);

            List<PostAPIResult> result = [];

            if (httpStatusCode == 200)
            {
                result.AddRange(JsonSerializer.Deserialize<List<PostAPIResult>>(json.Value?.ToString() ?? ""));
            }
            else
            {
                var error = JsonSerializer.Deserialize<FusionCoreAPIError>(json.Value?.ToString() ?? "");

                FusionCoreAPIError fusionCoreAPIError = new FusionCoreAPIError()
                {
                    error = error.error,
                    description = string.Join(",", error.descriptions)
                };

                result.Add(new PostAPIResult() { errors = [fusionCoreAPIError] });
            }

            return result;
        }
        public async Task<List<PostAPIResult>> ValidateDevice(List<AddDeviceInput> deviceList)
        {
            var (httpStatusCode, json) = await base.PostFusionNetCore($"/devices/api/{base._apiVersion}/devices/validation", deviceList).ConfigureAwait(false);

            List<PostAPIResult> result = [];

            if (httpStatusCode == 200)
            {
                result.AddRange(JsonSerializer.Deserialize<List<PostAPIResult>>(json.Value?.ToString() ?? ""));
            }
            else
            {
                var error = JsonSerializer.Deserialize<FusionCoreAPIError>(json.Value?.ToString() ?? "");

                FusionCoreAPIError fusionCoreAPIError = new FusionCoreAPIError()
                {
                    error = error.error,
                    description = string.Join(",", error.descriptions)
                };

                result.Add(new PostAPIResult() { errors = [fusionCoreAPIError] });
            }

            return result;
        }

        public async Task<List<PatchAPIResult>> PatchDevice(List<UpdateDeviceInput> deviceList)
        {
            var result = await base.PatchFusionNetCore($"/devices/api/{base._apiVersion}/devices", deviceList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "");
        }

        public async Task<List<Devices>?> GetDeviceList(string appCode, string search = null)
        {
            var result = await base.GetFusionNetCore(appCode, $"/devices/api/{base._apiVersion}/devices?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<Devices>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions);
        }

        public async Task<List<DeviceResults.Result>> GetDeviceReport(InGetDeviceReport param)
        {
            string url = $"/reports/api/{base._apiVersion}/devices/records?deviceType={param.DeviceType}" +
                $"&startedTime={param.StartedTime}&endedTime={param.EndedTime}" +
                $"{(string.IsNullOrWhiteSpace(param.Pids) ? "" : $"&devicePids={param.Pids}")}" +
                $"{(string.IsNullOrWhiteSpace(param.PidLike) ? "" : $"&devicePidLike={param.PidLike}")}" +
                $"{(string.IsNullOrWhiteSpace(param.ObjectCodes) ? "" : $"&objectCodes={param.ObjectCodes}")}" +
                $"{(string.IsNullOrWhiteSpace(param.ObjectNameLike) ? "" : $"&objectNameLike={param.ObjectNameLike}")}" +
                $"{(string.IsNullOrWhiteSpace(param.ResourceIds) ? "" : $"&resourceIds={param.ResourceIds}")}" +
                $"{(string.IsNullOrWhiteSpace(param.LineType) ? "" : $"&lineType={param.LineType}")}" +
                $"{(param.Interval == 0 ? "" : $"&interval={param.Interval}")}";

            var result = await base.GetFusionNetCore(url).ConfigureAwait(false);

            string json = result?.Value?.ToString() ?? "";

            // 反序列化 JSON
            DeviceResults wrapper = JsonSerializer.Deserialize<DeviceResults>(json);

            // 获取结果列表
            List<DeviceResults.Result> results = wrapper.Results;

            return results;
        }

        public async Task<List<DeleteAPIResult>?> UnbondObject(string pid)
        {
            var result = await base.DeleteFusionNetCore($"/devices/api/{base._apiVersion}/devices/{pid}/objects").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result?.Value?.ToString() ?? "");
        }

        public async Task<List<DeviceType>?> GetDeviceTypeList()
        {
            if (_memoryCache.TryGetValue(_deviceTypesCacheKey, out List<DeviceType>? cachedDeviceTypeList))
            {
                return cachedDeviceTypeList;
            }

            // 缓存未命中，进入锁定代码块
            await _cacheLock.WaitAsync();
            try
            {
                // 再次检查是否已经被其他线程计算并放入缓存
                if (!_memoryCache.TryGetValue(_deviceTypesCacheKey, out cachedDeviceTypeList))
                {
                    // 缓存中没有数据，调用 GetFusionNetCore 获取数据
                    var responseContent = await base.GetFusionNetCore($"/devices/api/{base._apiVersion}/devices/types/menu").ConfigureAwait(false);

                    string responseContentString = responseContent?.Value?.ToString() ?? "";

                    cachedDeviceTypeList = JsonSerializer.Deserialize<List<DeviceType>>(responseContent?.Value?.ToString() ?? "");

                    // 设置内存缓存选项
                    var cacheEntryOptions = new MemoryCacheEntryOptions
                    {
                        /**
                         * CacheItemPriority 枚举有以下几个值：
                         * 
                         * Low：低优先级，在内存压力较大时会被优先移除。
                         * Normal：普通优先级，默认值。
                         * High：高优先级，在内存压力较大时会尽可能保留。
                         * NeverRemove：永不移除，除非手动删除或缓存服务被释放。
                         */
                        Priority = CacheItemPriority.NeverRemove // 通常FusionCore的資料不會變動，所以設定為永不移除，如果有變動，HMN 會重啟服務
                    };

                    _memoryCache.Set(_deviceTypesCacheKey, cachedDeviceTypeList, cacheEntryOptions);
                }
            }
            finally
            {
                _cacheLock.Release();
            }

            return cachedDeviceTypeList;
        }

        public async Task<List<DevicePositions>?> GetDevicePositionsList()
        {
            var result = await base.GetFusionNetCore($"/devices/api/{base._apiVersion}/devices/positions").ConfigureAwait(false);

            string responseContentString = result?.Value?.ToString() ?? "";

            return JsonSerializer.Deserialize<List<DevicePositions>>(JsonSerializer.Deserialize<Dictionary<string, object>>(responseContentString)?["results"].ToString() ?? "", _jsonSerializerOptions);
        }

        public async Task<List<DeleteAPIResult>?> DeleteDevice(string pids)
        {
            string url = $"/devices/api/{base._apiVersion}/devices/{pids}/active";

            var result = await base.DeleteFusionNetCore(url).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result.Value?.ToString() ?? "");
        }

        public async Task<int> GetTrajectoryCount(TrajectoryInput param)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/trajectories/api/{base._apiVersion}/trajectories/queries", param).ConfigureAwait(false);

            string responseContentString = JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["count"].ToString() ?? "";

            return int.Parse(responseContentString);
        }

        public async Task<List<TrajectoryOutput>> GetTrajectoryList(TrajectoryInput param)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/trajectories/api/{base._apiVersion}/trajectories/queries", param).ConfigureAwait(false);

            string responseContentString = JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "";

            return JsonSerializer.Deserialize<List<TrajectoryOutput>>(responseContentString, _jsonSerializerOptions);
        }

        public List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults)
        {
            return base.GetFusionError(commonAPIResults);
        }
    }
    public class EventServiceNet : FusionNetBase, IEventService
    {
        public EventServiceNet(IHttpContextAccessor accessor,
                              IConfiguration configuration,
                              IHttpClientFactory httpClientFactory,
                              IDataAccessService dataAccessService) : base(accessor, configuration, httpClientFactory, dataAccessService)
        {
        }

        public async Task<List<PostAPIResult>?> AddActiveEvent(List<PostFusionEventInput> _eventData)
        {
            try
            {
                var (httpStatusCode, json) = await base.PostFusionNetCore($"/events/api/{base._apiVersion}/events", _eventData).ConfigureAwait(false);

                List<PostAPIResult> result = [];

                if (httpStatusCode == 200)
                {
                    result.AddRange(JsonSerializer.Deserialize<List<PostAPIResult>>(json.Value?.ToString() ?? ""));
                }
                else
                {
                    var error = JsonSerializer.Deserialize<FusionCoreAPIError>(json.Value?.ToString() ?? "");

                    result.Add(new PostAPIResult() { errors = [error] });
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<DeleteAPIResult>?> DeleteActiveEvent(string eventCode)
        {
            var result = await base.DeleteFusionNetCore($"/events/api/{base._apiVersion}/events/{eventCode}/active").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result?.Value?.ToString() ?? "");
        }

        public async Task<List<PatchAPIResult>> PatchActiveEvents(List<PatchFusionEventInput> _eventData)
        {
            var result = await base.PatchFusionNetCore($"/events/api/{base._apiVersion}/events", _eventData).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public async Task<HashSet<string>> ExistEvents(List<string> eventCodeList)
        {
            var codeSet = new HashSet<string>();

            string eventCodes = string.Join(",", eventCodeList);
            var result = await base.GetFusionNetCore($"/events/api/{base._apiVersion}/events?search=code in " + eventCodes).ConfigureAwait(false);

            // 反序列化為 Dictionary
            var resultDict = JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "");
            if (resultDict != null && resultDict.TryGetValue("results", out var results))
            {
                // 檢查 results 是否為列表
                if (results is JsonElement resultsElement && resultsElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var item in resultsElement.EnumerateArray())
                    {
                        // 確保 item 是物件並包含 code 屬性
                        if (item.ValueKind == JsonValueKind.Object && item.TryGetProperty("code", out var codeProperty))
                        {
                            // 將 code 加入 Set
                            codeSet.Add(codeProperty.GetString());
                        }
                    }
                }
            }

            return codeSet;
        }

    }

    public class StationServiceNet(IHttpContextAccessor accessor,
                             IConfiguration configuration,
                             IHttpClientFactory httpClientFactory,
                             JsonSerializerOptions jsonSerializerOptions,
                             IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), IStationService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        public async Task<List<Stations>?> GetStationListWithDevices(string stationSid)
        {
            var result = await base.GetFusionNetCore($"/stations/api/{base._apiVersion}/stations/devices?search=sid in {stationSid}").ConfigureAwait(false);

            var coreStationList = JsonSerializer.Deserialize<List<Stations>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions);

            return coreStationList;
        }

        public async Task<List<Stations>?> GetStationList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/stations/api/{base._apiVersion}/stations?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<Stations>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions);
        }
        public async Task<List<ConfigurationsCompatible>?> GetConfigurationsCompatible(string search = null, string resourceIds = null)
        {
            // resourceIds 一定要有值，否則會報錯
            string url = $"/stations/api/{base._apiVersion}/stations/configurationsCompatible?resourceIds=" + (resourceIds ?? "/10001/0/24") + "&inlinecount=true&search=" + (search ?? "");

            var result = await base.GetFusionNetCore(url).ConfigureAwait(false);

            // 反序列化為 Dictionary
            var resultDict = JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "");

            if (resultDict != null && resultDict.TryGetValue("results", out var results))
            {
                // 如果包含 "results" key，繼續反序列化為目標類型
                return JsonSerializer.Deserialize<List<ConfigurationsCompatible>>(results.ToString() ?? "", _jsonSerializerOptions);
            }

            // 如果沒有 "results" key，返回空列表
            return new List<ConfigurationsCompatible>();
        }

        public async Task<List<PostAPIResult>> AddStation(List<AddStationInput> stationList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/stations/api/{base._apiVersion}/stations", stationList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<PostAPIResult>> AddStationValidation(List<AddStationInput> stationList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/stations/api/{base._apiVersion}/stations/validation", stationList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<PatchAPIResult>> PatchStation(List<IDictionary<string, object>> stationList)
        {
            var result = await base.PatchFusionNetCore($"/stations/api/{base._apiVersion}/stations", stationList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<PatchAPIResult>> PatchConfiguration(List<PatchConfigurationInput> stationList)
        {
            var result = await base.PatchFusionNetCore($"/stations/api/{base._apiVersion}/stations/configurations", stationList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<DeleteAPIResult>> DeleteStation(string sids)
        {
            string url = $"/stations/api/{base._apiVersion}/stations/{sids}/active";

            var result = await base.DeleteFusionNetCore(url).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }
        public async Task<List<PostAPIResult>> Command(List<StationCommandInput> commandList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/stations/api/{base._apiVersion}/stations/commands", commandList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }
        public async Task<List<PostAPIResult>> Reboot(List<string> sidList)
        {
            List<StationCommandInput> commandList = sidList.Select(e => new StationCommandInput()
            {
                sid = e,
                command = new Command() { resourceId = "/1030/0/4", value = "1" },
                timeout = 3000
            }).ToList();

            var result = await Command(commandList).ConfigureAwait(false);

            return result;
        }
        public List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults)
        {
            return base.GetFusionError(commonAPIResults);
        }
    }

    public class PlaneServiceNet(IHttpContextAccessor accessor,
                             IConfiguration configuration,
                             IHttpClientFactory httpClientFactory,
                             JsonSerializerOptions jsonSerializerOptions,
                             IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), IPlaneService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        public async Task<List<Planes>?> GetPlaneList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/stations/api/{base._apiVersion}/planes?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<Planes>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions);
        }

        public async Task<List<PostAPIResult>> AddPlane(List<PostPlaneInput> planeList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/stations/api/{base._apiVersion}/planes", planeList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<PostAPIResult>> AddPlaneValidation(List<PostPlaneInput> planeList)
        {
            var (httpStatusCode, result) = await base.PostFusionNetCore($"/stations/api/{base._apiVersion}/planes/validation", planeList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PostAPIResult>>(result.Value?.ToString() ?? "") ?? [];
        }

        public async Task<PutAPIResult> PutPlane(PutPlaneInput planeImage)
        {
            var uploadImageRequestContent = new MultipartFormDataContent();
            var fileContent = new StreamContent(planeImage.Image.OpenReadStream());
            fileContent.Headers.ContentType = new MediaTypeHeaderValue(planeImage.Image.ContentType);
            uploadImageRequestContent.Add(fileContent, "image", planeImage.Image.FileName);

            var (httpStatusCode, result) = await base.PutFusionNetCoreWithMultipart($"/stations/api/{base._apiVersion}/planes/{planeImage.code}/map", uploadImageRequestContent).ConfigureAwait(false);

            return JsonSerializer.Deserialize<PutAPIResult>(result.Value?.ToString() ?? "");
        }

        public async Task<List<PatchAPIResult>> PatchPlane(List<PatchPlaneInput> planeList)
        {
            var result = await base.PatchFusionNetCore($"/stations/api/{base._apiVersion}/planes", planeList).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<PatchAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public async Task<List<DeleteAPIResult>> DeletePlane(string planeCodes)
        {
            string url = $"/stations/api/{base._apiVersion}/planes/{planeCodes}/active";

            var result = await base.DeleteFusionNetCore(url).ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<DeleteAPIResult>>(result?.Value?.ToString() ?? "") ?? [];
        }

        public List<ReturnError> GetFusionError(List<CommonAPIResult> commonAPIResults)
        {
            return base.GetFusionError(commonAPIResults);
        }
    }

    public class LicenseServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          JsonSerializerOptions jsonSerializerOptions,
                          IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), ILicenseService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        public async Task<List<GetLicenseServiceOutput>> GetServiceList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/licenses/api/{base._apiVersion}/licenses/services?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<GetLicenseServiceOutput>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions) ?? [];
        }

        public async Task<List<GetLicenseNotifyConfigOutput>> GetNotifyConfigList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/licenses/api/{base._apiVersion}/notify/configs?search=" + search ?? "").ConfigureAwait(false);
            
            return JsonSerializer.Deserialize<List<GetLicenseNotifyConfigOutput>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions) ?? [];
        }
    }

    public class SystemServiceNet(IHttpContextAccessor accessor,
                          IConfiguration configuration,
                          IHttpClientFactory httpClientFactory,
                          JsonSerializerOptions jsonSerializerOptions,
                          IDataAccessService dataAccessService) : FusionNetBase(accessor, configuration, httpClientFactory, dataAccessService), ISystemService
    {
        private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions;

        public async Task<List<GetSystemConfigurationsOutput>> GetSystemConfigurationList(string search = null)
        {
            var result = await base.GetFusionNetCore($"/system/api/{base._apiVersion}/system/configurations?search=" + search ?? "").ConfigureAwait(false);

            return JsonSerializer.Deserialize<List<GetSystemConfigurationsOutput>>(JsonSerializer.Deserialize<Dictionary<string, object>>(result?.Value?.ToString() ?? "")?["results"].ToString() ?? "", _jsonSerializerOptions) ?? [];
        }
    }
}
