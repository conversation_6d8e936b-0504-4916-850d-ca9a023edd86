<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第三方通知 API 文件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        .endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .method {
            font-weight: bold;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            margin-right: 10px;
            display: inline-block;
            width: 80px;
            text-align: center;
        }
        .get { background-color: #61affe; }
        .post { background-color: #49cc90; }
        .patch { background-color: #fca130; }
        .delete { background-color: #f93e3e; }
        
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .description {
            margin-bottom: 15px;
        }
        .url {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #e83e8c;
        }
    </style>
</head>
<body>
    <h1>第三方通知 API 文件</h1>
    
    <h2>基本信息</h2>
    <ul>
        <li><strong>基礎路徑</strong>: <code>/NotifyThird</code></li>
        <li><strong>需要授權</strong>: 是 (需要 JWT Token)</li>
    </ul>
    
    <h2>API 端點</h2>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> <span class="url">/NotifyThird/thirds/validate</span></h3>
        <div class="description">驗證第三方通知資料是否有效</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "AreaCode": "A1",
        "NotifyType": "ENS",
        "ThirdCode": "THIRDTEST",
        "ThirdName": "第三方測試",
        "URL_MAC": "https://example.com/notify"
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 201,
    "result": true,
    "model": null,
    "data": null,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> <span class="url">/NotifyThird/thirds</span></h3>
        <div class="description">新增第三方通知資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "AreaCode": "A1",
        "NotifyType": "ENS",
        "ThirdCode": "THIRDTEST",
        "ThirdName": "第三方測試",
        "URL_MAC": "https://example.com/notify"
    }
]</code></pre>
        
        <h4>成功回應 (201 Created):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 201,
    "result": true,
    "model": null,
    "data": null,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method patch">PATCH</span> <span class="url">/NotifyThird/thirds</span></h3>
        <div class="description">更新現有第三方通知資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "ThirdCode": "THIRDTEST",
        "ThirdName": "第三方測試更新",
        "URL_MAC": "https://example.com/notify/updated"
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": true,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method delete">DELETE</span> <span class="url">/NotifyThird/thirds</span></h3>
        <div class="description">刪除第三方通知資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "ThirdCode": "THIRDTEST"
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": true,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> <span class="url">/NotifyThird/thirds</span></h3>
        <div class="description">查詢第三方通知資料，支持分頁和排序</div>
        
        <h4>查詢參數:</h4>
        <table>
            <tr>
                <th>參數名</th>
                <th>類型</th>
                <th>必填</th>
                <th>描述</th>
            </tr>
            <tr>
                <td>page</td>
                <td>string</td>
                <td>否</td>
                <td>頁碼 (默認為 1)</td>
            </tr>
            <tr>
                <td>size</td>
                <td>string</td>
                <td>否</td>
                <td>每頁記錄數 (默認為 0，表示不分頁)</td>
            </tr>
            <tr>
                <td>sort</td>
                <td>string</td>
                <td>否</td>
                <td>排序條件 (格式為 "欄位:方向"，例如 "ModifyDate:desc")</td>
            </tr>
            <tr>
                <td>AreaCode</td>
                <td>string</td>
                <td>否</td>
                <td>區域代碼</td>
            </tr>
            <tr>
                <td>ThirdCode</td>
                <td>string</td>
                <td>否</td>
                <td>第三方代碼</td>
            </tr>
            <tr>
                <td>ThirdName</td>
                <td>string</td>
                <td>否</td>
                <td>第三方名稱</td>
            </tr>
            <tr>
                <td>NotifyType</td>
                <td>string</td>
                <td>否</td>
                <td>通知類型 ("ENS" 或 "Display")</td>
            </tr>
        </table>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": {
        "recordTotal": 1,
        "recordList": [
            {
                "Id": 1,
                "NotifyType": "ENS",
                "ThirdCode": "THIRDTEST",
                "ThirdName": "第三方測試更新",
                "URL_MAC": "https://example.com/notify/updated",
                "AreaName": "A1",
                "AreaCode": "A1",
                "CreateDate": "2025-05-16T15:02:33",
                "CreateUserAccount": "ADMIN",
                "ModifyDate": "2025-05-16T15:05:43",
                "ModifyUserAccount": "ADMIN"
            }
        ]
    },
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <h2>資料驗證規則</h2>
    
    <h3>第三方代碼 (ThirdCode)</h3>
    <ul>
        <li>必填</li>
        <li>格式必須符合正則表達式 <code>^[a-zA-Z0-9_:@-]+$</code></li>
        <li>最大長度為 20 字符</li>
        <li>必須唯一</li>
    </ul>
    
    <h3>第三方名稱 (ThirdName)</h3>
    <ul>
        <li>必填</li>
        <li>最大長度為 50 字符</li>
    </ul>
    
    <h3>區域代碼 (AreaCode)</h3>
    <ul>
        <li>必填</li>
        <li>必須存在於 Area 表中</li>
    </ul>
    
    <h3>通知類型 (NotifyType)</h3>
    <ul>
        <li>必填</li>
        <li>只能是 "ENS" 或 "Display"</li>
    </ul>
    
    <h3>URL/MAC (URL_MAC)</h3>
    <ul>
        <li>必填</li>
        <li>最大長度為 50 字符</li>
        <li>當 NotifyType 為 "Display" 時，必須唯一</li>
    </ul>
    
    <h2>錯誤回應</h2>
    <p>當請求無效時，API 將返回 400 Bad Request 狀態碼，並在回應中包含詳細的錯誤信息：</p>
    
    <pre><code>{
  "httpStatus": 400,
  "result": false,
  "data": [
    {
      "index": 0,
      "code": "string",
      "errors": [
        {
          "index": 0,
          "code": "string",
          "error": "string",
          "message": "string",
          "innerMsg": "string",
          "details": null
        }
      ]
    }
  ]
}</code></pre>

</body>
</html>