﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Web.Models.Controller;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;
using Web.Constant;
using Web.Models.Controller.SystemEvent;
using Web.Models.Service.Fusion;

namespace Web.Controller;

/// <summary>
/// 系統事件
/// </summary>
[Route("[controller]")]
[Authorize]
public class SystemEventController(IDataAccessService dataAccessService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice,
                                    IEventService eventService) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IEventService _eventService = eventService;

    /// <summary>
    /// 新增系統事件
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("events")]
    [RequestParamListDuplicate("ServiceCode,DeviceType")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateSystemEvent([FromBody] List<CreateSystemEvent> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "SystemEvent Data Validated, start to append.");

        // 新增至 fusion event，回傳是新增成功的 CreateSystemEvent 列表
        List<CreateSystemEvent> successCreateSystemEventList = await CreateEvents(paramList);

        foreach (CreateSystemEvent param in successCreateSystemEventList)
        {
            SystemEvent se = new SystemEvent
            {
                AppCode = _user.AppCode,
                ServiceCode = param.ServiceCode,
                EventCode = getEventCodeByServiceCodeAndDeviceType(param.ServiceCode, param.DeviceType, appCode),
                EventName = param.EventName,
                DeviceType = param.DeviceType,
                Enable = param.Enable == "Y" ? true : false,
                Threshold = param.Threshold,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
            };

            // 新增 SystemEvent
            await _dataAccessService.CreateAsync<SystemEvent>(se);

            // 取得新增成功的 SystemEvent
            var systemEvent = _dataAccessService.Fetch<SystemEvent>(e => e.AppCode == appCode && e.ServiceCode == se.ServiceCode && e.DeviceType == se.DeviceType).FirstOrDefault();

            // 新增排除區間列表
            List<Web.Models.Controller.SystemEvent.ExclusionPeriod> exclusionPeriodList = param.ExclusionPeriodList;
            if (systemEvent != null && exclusionPeriodList != null && exclusionPeriodList.Count > 0)
            {
                List<Repository.Models.Entities.ExclusionPeriod> excludsionPeriods = exclusionPeriodList.Select(ep => new Repository.Models.Entities.ExclusionPeriod()
                {
                    AppCode = _user.AppCode,
                    SystemEventId = systemEvent.Id,
                    Weekly = ep.Weekly,
                    StartTime = ep.StartTime,
                    EndTime = ep.EndTime,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                }).ToList();

                // 批次新增
                await _dataAccessService.CreateRangeAsync(excludsionPeriods);
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "SystemEvent Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    private string getEventCodeByServiceCodeAndDeviceType(string serviceCode, string deviceType, string appCode)
    {
        string eventCode = $"@{appCode}@{serviceCode}@1";
        if (serviceCode == "AbnormalDevice")
        {
            eventCode = $"@{appCode}@{serviceCode}@{deviceType}@1";
        }
        else if (new[] { "LowBattery", "LossSignal" }.Contains(serviceCode))
        {
            eventCode = $"@{appCode}@{serviceCode}@{deviceType}";
        }
        return eventCode;
    }

    private async Task<List<CreateSystemEvent>> CreateEvents(List<CreateSystemEvent> paramList)
    {
        List<PostFusionEventInput> postFusionEventInputs = [];

        // 新增ObjectEvent
        foreach (CreateSystemEvent param in paramList)
        {
            PostFusionEventInput postFusionEventInput = new()
            {
                enable = true,
                code = getEventCodeByServiceCodeAndDeviceType(param.ServiceCode, param.DeviceType, _user.AppCode),
                name = param.EventName,
                serviceCode = param.ServiceCode,
                sponsorDevice = new PostFusionEventInput.SponsorDevice
                {
                    deviceTypes = string.IsNullOrEmpty(param.DeviceType) ? [] : [param.DeviceType]
                },
                exclusionPeriod = param.ExclusionPeriodList.Select(ep => new PostFusionEventInput.ExclusionPeriod
                {
                    frequency = "weekly",
                    startsAt = ep.StartTime,
                    finishedAt = ep.EndTime,
                    value = "[" + ep.Weekly + "]",
                    enable = true
                }).ToList()
            };

            if (new[] { "LowBattery", "LossSignal" }.Contains(param.ServiceCode))
            {
                postFusionEventInput.arguments =
                [
                    new (){key = "threshold", value = param.Threshold.HasValue ? param.Threshold.Value.ToString() : ""},
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "rssiDelta1", value = "0"},
                    new (){key = "rssiDelta2", value = "0"},
                ];
            }
            else if (new[] { "AbnormalDevice", "AbnormalStation" }.Contains(param.ServiceCode))
            {
                postFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "rssiDelta1", value = "0"},
                    new (){key = "rssiDelta2", value = "0"},
                ];
            }

            postFusionEventInputs.Add(postFusionEventInput);
        }

        List<PostAPIResult> postEventResults = new List<PostAPIResult>();

        // 取得不存在於 existEventCodeSet 的子列表
        if (postFusionEventInputs.Any())
        {
            var postResults = await _eventService.AddActiveEvent(postFusionEventInputs);
            postEventResults.AddRange(postResults);
        }

        // 取出 Fusion 新增成功的 CreateSystemEvent
        List<CreateSystemEvent> successCreateSystemEventList = paramList.Where(e => postEventResults.Any(p => p.code == getEventCodeByServiceCodeAndDeviceType(e.ServiceCode, e.DeviceType, _user.AppCode) && (p.errors == null || p.errors.Count == 0))).ToList();

        return successCreateSystemEventList;
    }

    [HttpPatch("events")]
    [RequestParamListDuplicate("ServiceCode,DeviceType")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateSystemEvent([FromBody] List<UpdateSystemEvent> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 取得 ServiceCode 與 SystemEvent Entity 的映射
        var systemEventDict = _dataAccessService
            .Fetch<SystemEvent>(e => e.AppCode == _user.AppCode) // 先查 AppCode 篩選
            .ToList() // 轉成 List，強制讓後續 Any() 在 C# 層面運行
            .Where(e => paramList.Any(param => e.ServiceCode == param.ServiceCode && e.DeviceType == param.DeviceType))
            .ToDictionary(e => $"{e.ServiceCode}@{e.DeviceType}", e => e);

        // 更新至 fusion event，回傳是更新成功的 UpdateSystemEvent 列表
        List<UpdateSystemEvent> successUpdateSystemEventList = await UpdateEvents(paramList, systemEventDict);

        _dataAccessService.BeginTransaction();

        foreach (UpdateSystemEvent param in successUpdateSystemEventList)
        {
            SystemEvent systemEvent = _dataAccessService.Fetch<SystemEvent>(e => e.AppCode == _user.AppCode && e.ServiceCode == param.ServiceCode && e.DeviceType == param.DeviceType).AsTracking().First();

            List<string> updateField = new List<string>();

            if (param.EventName != null)
            {
                updateField.Add("EventName");
                systemEvent.EventName = param.EventName;
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                systemEvent.Enable = param.Enable == "Y";
            }

            if (param.Threshold != null)
            {
                updateField.Add("Threshold");
                systemEvent.Threshold = param.Threshold;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            systemEvent.ModifyDate = DateTime.Now;
            systemEvent.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<SystemEvent>(systemEvent, updateField.ToArray());

            // 如果 ExclusionPeriodList 為 null 就不更新對象資料
            if (param.ExclusionPeriodList == null)
                continue;

            // 刪除原有的排除區間列表
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ExclusionPeriod>(e => e.AppCode == _user.AppCode && e.SystemEventId == systemEvent.Id);

            // 新增排除區間列表
            List<Web.Models.Controller.SystemEvent.ExclusionPeriod> exclusionPeriodList = param.ExclusionPeriodList;
            if (systemEvent != null && exclusionPeriodList != null && exclusionPeriodList.Count > 0)
            {
                List<Repository.Models.Entities.ExclusionPeriod> excludsionPeriods = exclusionPeriodList.Select(ep => new Repository.Models.Entities.ExclusionPeriod()
                {
                    AppCode = _user.AppCode,
                    SystemEventId = systemEvent.Id,
                    Weekly = ep.Weekly,
                    StartTime = ep.StartTime,
                    EndTime = ep.EndTime,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                }).ToList();

                // 批次新增
                await _dataAccessService.CreateRangeAsync(excludsionPeriods);
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "SystemEvent Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    private async Task<List<UpdateSystemEvent>> UpdateEvents(List<UpdateSystemEvent> paramList, Dictionary<string, SystemEvent> systemEventDict)
    {
        List<PatchFusionEventInput> patchFusionEventInputs = [];

        // 新增ObjectEvent
        foreach (UpdateSystemEvent param in paramList)
        {
            SystemEvent systemEvent = systemEventDict[$"{param.ServiceCode}@{param.DeviceType}"];

            PatchFusionEventInput patchFusionEventInput = new()
            {
                enable = true,
                code = systemEvent.EventCode,
                name = param.EventName,
                serviceCode = param.ServiceCode,
                sponsorDevice = new PatchFusionEventInput.SponsorDevice
                {
                    deviceTypes = string.IsNullOrEmpty(systemEvent.DeviceType) ? [] : [systemEvent.DeviceType]
                },
                exclusionPeriod = param.ExclusionPeriodList.Select(ep => new PatchFusionEventInput.ExclusionPeriod
                {
                    frequency = "weekly",
                    startsAt = ep.StartTime,
                    finishedAt = ep.EndTime,
                    value = "[" + ep.Weekly + "]",
                    enable = true
                }).ToList()
            };

            if (new[] { "LowBattery", "LossSignal" }.Contains(param.ServiceCode))
            {
                patchFusionEventInput.arguments =
                [
                    new (){key = "threshold", value = param.Threshold.HasValue ? param.Threshold.Value.ToString() : ""},
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "rssiDelta1", value = "0"},
                    new (){key = "rssiDelta2", value = "0"},
                ];
            }
            else if (new[] { "AbnormalDevice", "AbnormalStation" }.Contains(param.ServiceCode))
            {
                patchFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "rssiDelta1", value = "0"},
                    new (){key = "rssiDelta2", value = "0"},
                ];
            }

            patchFusionEventInputs.Add(patchFusionEventInput);
        }

        List<PatchAPIResult> patchEventResults = new List<PatchAPIResult>();

        if (patchFusionEventInputs.Any())
        {
            var postResults = await _eventService.PatchActiveEvents(patchFusionEventInputs);
            patchEventResults.AddRange(postResults);
        }

        // 取出 Fusion 更新成功的 UpdateSystemEvent
        List<UpdateSystemEvent> successUpdateSystemEventList = paramList.Where(e => patchEventResults.Any(p => p.code == systemEventDict[$"{e.ServiceCode}@{e.DeviceType}"].EventCode && (p.errors == null || p.errors.Count == 0))).ToList();

        return successUpdateSystemEventList;
    }

    [HttpDelete("events")]
    [RequestParamListDuplicate("ServiceCode,DeviceType")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteSystemEvent([FromBody] List<DeleteSystemEvent> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 取得 ServiceCode 與 SystemEvent Entity 的映射
        // 取得 ServiceCode 與 SystemEvent Entity 的映射
        var systemEventDict = _dataAccessService
            .Fetch<SystemEvent>(e => e.AppCode == _user.AppCode) // 先查 AppCode 篩選
            .ToList() // 轉成 List，強制讓後續 Any() 在 C# 層面運行
            .Where(e => paramList.Any(param => e.ServiceCode == param.ServiceCode && e.DeviceType == param.DeviceType))
            .ToDictionary(e => $"{e.ServiceCode}@{e.DeviceType}", e => e);

        // 刪除 fusion event，回傳是刪除成功的 DeleteSystemEvent 列表
        List<DeleteSystemEvent> successDeleteSystemEventList = await DeleteEvents(paramList, systemEventDict);

        // 開始刪除系統事件資料
        _logService.Logging("info", logActionName, requestUUID, "SystemEvent Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteSystemEvent param in successDeleteSystemEventList)
        {
            SystemEvent systemEvent = _dataAccessService.Fetch<SystemEvent>(e => e.AppCode == _user.AppCode && e.ServiceCode == param.ServiceCode && e.DeviceType == param.DeviceType).AsTracking().First();

            // 刪除系統事件
            await _dataAccessService.DeleteAsync<SystemEvent>(e => e.AppCode == _user.AppCode && e.ServiceCode == param.ServiceCode && e.DeviceType == param.DeviceType);

            // 刪除排除區間列表
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ExclusionPeriod>(e => e.AppCode == _user.AppCode && e.SystemEventId == systemEvent.Id);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "SystemEvent Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    private async Task<List<DeleteSystemEvent>> DeleteEvents(List<DeleteSystemEvent> paramList, Dictionary<string, SystemEvent> systemEventDict)
    {
        List<string> deleteFusionEventInputs = [];

        // 新增ObjectEvent
        foreach (DeleteSystemEvent param in paramList)
        {
            SystemEvent systemEvent = systemEventDict[$"{param.ServiceCode}@{param.DeviceType}"];
            string eventCode = systemEvent.EventCode;

            deleteFusionEventInputs.Add(eventCode);
        }

        List<DeleteAPIResult> deleteEventResults = new List<DeleteAPIResult>();

        if (deleteFusionEventInputs.Any())
        {
            foreach (string eventCode in deleteFusionEventInputs)
            {
                var deleteResults = await _eventService.DeleteActiveEvent(eventCode);
                deleteEventResults.AddRange(deleteResults);
            }
        }

        // 取出 Fusion 刪除成功的 DeleteSystemEvent
        List<DeleteSystemEvent> successDeleteSystemEventList = paramList.Where(e => deleteEventResults.Any(p => p.code == systemEventDict[$"{e.ServiceCode}@{e.DeviceType}"].EventCode && (p.errors == null || p.errors.Count == 0))).ToList();

        return successDeleteSystemEventList;
    }

    [HttpGet("events")]
    public async Task<IActionResult> RetrieveSystemEvent([FromQuery] RetrieveSystemEvent queryParam)
    {
        RetrieveSystemEvent param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var systemEventList = _dataAccessService.Fetch<SystemEvent>(x => x.AppCode == _user.AppCode);
        var exclusionPeriodList = _dataAccessService.Fetch<Repository.Models.Entities.ExclusionPeriod>(x => x.AppCode == _user.AppCode);

        // 處理 param.ServiceCode 參數為 serviceCodeList
        List<string> serviceCodeList = param.ServiceCode?.Split(',').ToList() ?? new List<string>();

        // 處理 param.EventCode 參數為 eventCodeList
        List<string> eventCodeList = param.EventCode?.Split(',').ToList() ?? new List<string>();

        // 處理 param.EventName 參數為 eventNameList
        List<string> eventNameList = param.EventName?.Split(',').ToList() ?? new List<string>();

        // 處理 param.DeviceType 參數為 deviceTypeList
        List<string> deviceTypeList = param.DeviceType?.Split(',').ToList() ?? new List<string>();

        var query = (from se in systemEventList
                     select new
                     {
                         se.Id,
                         se.AppCode,
                         se.ServiceCode,
                         se.EventCode,
                         se.EventName,
                         se.DeviceType,
                         Enable = (se.Enable == true) ? "Y" : "N",
                         se.Threshold,
                         exclusionPeriods = (from ep in exclusionPeriodList
                                             where ep.SystemEventId == se.Id
                                             select new
                                             {
                                                 ep.Weekly,
                                                 ep.StartTime,
                                                 ep.EndTime
                                             }).ToList(),
                         se.CreateDate,
                         se.CreateUserAccount,
                         se.ModifyDate,
                         se.ModifyUserAccount
                     })
            .Where(x => (!serviceCodeList.Any() || serviceCodeList.Any(y => x.ServiceCode.ToUpper().Contains(y.ToUpper())))
                     && (!eventCodeList.Any() || eventCodeList.Any(y => x.EventCode.ToUpper().Contains(y.ToUpper())))
                     && (!eventNameList.Any() || eventNameList.Any(y => x.EventName.ToUpper().Contains(y.ToUpper())))
                     && (!deviceTypeList.Any() || deviceTypeList.Any(y => x.DeviceType.ToUpper().Contains(y.ToUpper())))
                     && (param.Enable == null || x.Enable == param.Enable)
                     && (param.Threshold == null || x.Threshold == double.Parse(param.Threshold)));

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
