<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件通知設定 API 文件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        .endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .method {
            font-weight: bold;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            margin-right: 10px;
            display: inline-block;
            width: 80px;
            text-align: center;
        }
        .get { background-color: #61affe; }
        .post { background-color: #49cc90; }
        .patch { background-color: #fca130; }
        .delete { background-color: #f93e3e; }
        
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .description {
            margin-bottom: 15px;
        }
        .url {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #e83e8c;
        }
    </style>
</head>
<body>
    <h1>事件通知設定 API 文件</h1>
    
    <h2>基本信息</h2>
    <ul>
        <li><strong>基礎路徑</strong>: <code>/EventNotifySetting</code></li>
        <li><strong>需要授權</strong>: 是 (需要 JWT Token)</li>
    </ul>
    
    <h2>API 端點</h2>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> <span class="url">/EventNotifySetting/settings/validate</span></h3>
        <div class="description">驗證事件通知設定資料是否有效</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "AreaCode": "A1",
        "ServiceCode": "SensorDataDriven",
        "SddResource": "1",
        "Description": "測試事件通知設定",
        "BuildingCode": "B_492669319",
        "PlaneCode": "P_293875600",
        "DeptCode": "A1",
        "ObjectType": "1",
        "GroupCode": "wwww",
        "Enable": "Y",
        "EnableSchedule": "Y",
        "PositionNotify": "Y",
        "TraceTime": 30,
        "ENSMessage": "ENS訊息內容",
        "DisplayMessage1": "顯示訊息1",
        "DisplayMessage2": "顯示訊息2",
        "EmailSubject": "郵件主題",
        "EmailMessage": "郵件內容",
        "NotifyMessage": "通知訊息",
        "SMSMessage": "簡訊內容",
        "ScheduleList": [
            {
                "Weekly": "1,2,3,4,5",
                "StartTime": "08:00",
                "EndTime": "18:00"
            }
        ],
        "ContactList": [
            {
                "NotifyType": "Email",
                "Source": "2",
                "ContactCode": "BRUCETEST"
            }
        ],
        "ThirdList": [
            {
                "NotifyType": "ENS",
                "ThirdCode": "test001"
            }
        ],
        "HeaderList": [
            {
                "NotifyCode": "BRUCETEST"
            }
        ]
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "A1",
            "A2",
            "A3"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": [],
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
</body>
</html>
    <div class="endpoint">
        <h3><span class="method post">POST</span> <span class="url">/EventNotifySetting/settings</span></h3>
        <div class="description">新增事件通知設定資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "AreaCode": "A1",
        "ServiceCode": "SensorDataDriven",
        "SddResource": "1",
        "Description": "測試事件通知設定",
        "BuildingCode": "B_492669319",
        "PlaneCode": "P_293875600",
        "DeptCode": "A1",
        "ObjectType": "1",
        "GroupCode": "wwww",
        "Enable": "Y",
        "EnableSchedule": "Y",
        "PositionNotify": "Y",
        "TraceTime": 30,
        "ENSMessage": "ENS訊息內容",
        "DisplayMessage1": "顯示訊息1",
        "DisplayMessage2": "顯示訊息2",
        "EmailSubject": "郵件主題",
        "EmailMessage": "郵件內容",
        "NotifyMessage": "通知訊息",
        "SMSMessage": "簡訊內容",
        "ScheduleList": [
            {
                "Weekly": "1,2,3,4,5",
                "StartTime": "08:00",
                "EndTime": "18:00"
            }
        ],
        "ContactList": [
            {
                "NotifyType": "Email",
                "Source": "2",
                "ContactCode": "BRUCETEST"
            }
        ],
        "ThirdList": [
            {
                "NotifyType": "ENS",
                "ThirdCode": "test001"
            }
        ],
        "HeaderList": [
            {
                "NotifyCode": "BRUCETEST"
            }
        ]
    }
]</code></pre>
        
        <h4>成功回應 (201 Created):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 201,
    "result": true,
    "model": null,
    "data": null,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method patch">PATCH</span> <span class="url">/EventNotifySetting/settings</span></h3>
        <div class="description">更新現有事件通知設定資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "Id": 2,
        "AreaCode": "A1",
        "Description": "測試事件通知設定",
        "BuildingCode": "B_492669319",
        "PlaneCode": "P_293875600",
        "DeptCode": "A1",
        "ObjectType": "1",
        "GroupCode": "wwww",
        "Enable": "Y",
        "EnableSchedule": "Y",
        "PositionNotify": "Y",
        "TraceTime": 30,
        "ENSMessage": "ENS訊息內容內容內容內容",
        "DisplayMessage1": "顯示訊息訊息訊息1",
        "DisplayMessage2": "顯示訊息訊息訊息2",
        "EmailSubject": "郵件主題主題主題主題",
        "EmailMessage": "郵件內容內容內容內容",
        "NotifyMessage": "通知訊息訊息訊息訊息",
        "SMSMessage": "簡訊內容內容內容內容",
        "ScheduleList": [
            {
                "Weekly": "1,2,3,4,5",
                "StartTime": "08:00",
                "EndTime": "18:00"
            }
        ],
        "ContactList": [
            {
                "NotifyType": "Email",
                "Source": "2",
                "ContactCode": "BRUCETEST"
            }
        ],
        "ThirdList": [
            {
                "NotifyType": "ENS",
                "ThirdCode": "test001"
            }
        ],
        "HeaderList": [
            {
                "NotifyCode": "BRUCETEST"
            }
        ]
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": true,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
	    <div class="endpoint">
        <h3><span class="method delete">DELETE</span> <span class="url">/EventNotifySetting/settings</span></h3>
        <div class="description">刪除事件通知設定資料</div>
        
        <h4>請求體:</h4>
        <pre><code>[
    {
        "Id": 2
    }
]</code></pre>
        
        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": true,
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> <span class="url">/EventNotifySetting/settings</span></h3>
        <div class="description">查詢事件通知設定資料，支持分頁和排序</div>
        
        <h4>查詢參數:</h4>
        <table>
            <tr>
                <th>參數名</th>
                <th>類型</th>
                <th>必填</th>
                <th>描述</th>
            </tr>
            <tr>
                <td>page</td>
                <td>string</td>
                <td>否</td>
                <td>頁碼 (默認為 1)</td>
            </tr>
            <tr>
                <td>size</td>
                <td>string</td>
                <td>否</td>
                <td>每頁記錄數 (默認為 0，表示不分頁)</td>
            </tr>
            <tr>
                <td>sort</td>
                <td>string</td>
                <td>否</td>
                <td>排序條件 (格式為 "欄位:方向"，例如 "ModifyDate:desc")</td>
            </tr>
            <tr>
                <td>AreaCode</td>
                <td>string</td>
                <td>否</td>
                <td>區域代碼</td>
            </tr>
            <tr>
                <td>ServiceCode</td>
                <td>string</td>
                <td>否</td>
                <td>服務代碼</td>
            </tr>
            <tr>
                <td>SddResource</td>
                <td>string</td>
                <td>否</td>
                <td>SDD資源</td>
            </tr>
            <tr>
                <td>Description</td>
                <td>string</td>
                <td>否</td>
                <td>描述</td>
            </tr>
            <tr>
                <td>BuildingCode</td>
                <td>string</td>
                <td>否</td>
                <td>建築物代碼</td>
            </tr>
            <tr>
                <td>PlaneCode</td>
                <td>string</td>
                <td>否</td>
                <td>樓層代碼</td>
            </tr>
            <tr>
                <td>DeptCode</td>
                <td>string</td>
                <td>否</td>
                <td>部門代碼</td>
            </tr>
            <tr>
                <td>ObjectType</td>
                <td>string</td>
                <td>否</td>
                <td>物件類型</td>
            </tr>
            <tr>
                <td>GroupCode</td>
                <td>string</td>
                <td>否</td>
                <td>群組代碼</td>
            </tr>
            <tr>
                <td>Enable</td>
                <td>string</td>
                <td>否</td>
                <td>啟用狀態 ("Y" 或 "N")</td>
            </tr>
            <tr>
                <td>EnableSchedule</td>
                <td>string</td>
                <td>否</td>
                <td>啟用排程 ("Y" 或 "N")</td>
            </tr>
            <tr>
                <td>NotifyCode</td>
                <td>string</td>
                <td>否</td>
                <td>通知代碼</td>
            </tr>
            <tr>
                <td>ThirdCode</td>
                <td>string</td>
                <td>否</td>
                <td>第三方代碼</td>
            </tr>
        </table>
		        <h4>成功回應 (200 OK):</h4>
        <pre><code>{
    "requestUUID": null,
    "authorize": {
        "Account": "ADMIN",
        "AppCode": "hmn",
        "AreaCode": "A1",
        "AreaCodeList": [
            "A1",
            "A3",
            "A2"
        ],
        "DeptCode": "A1",
        "DeptCodeList": [
            "5B",
            "A1",
            "Test_05",
            "Test_03",
            "A3",
            "Test_09",
            "ccc",
            "TESTA1A5F01",
            "10W",
            "A3_03F",
            "A2",
            "TESTA1A5F",
            "14",
            "Test_08",
            "Test_06",
            "A3_03F2",
            "Test_02",
            "eee",
            "Test_04",
            "07E",
            "Kevin1",
            "Test_07",
            "TESTA1A2F",
            "107TEST",
            "bbb",
            "34OR",
            "Test_01"
        ],
        "IsAdmin": "A",
        "IsViewAllArea": true
    },
    "httpStatus": 200,
    "result": true,
    "model": null,
    "data": {
        "recordTotal": 1,
        "recordList": [
            {
                "Id": 2,
                "AreaCode": "A1",
                "AreaName": "A1",
                "ServiceCode": "SensorDataDriven",
                "SddResource": "1",
                "Description": "測試事件通知設定",
                "BuildingCode": "B_492669319",
                "PlaneCode": "P_293875600",
                "DeptCode": "A1",
                "ObjectType": "1",
                "GroupCode": "wwww",
                "EnableSchedule": "Y",
                "PositionNotify": "Y",
                "TraceTime": 30,
                "ENSMessage": "ENS訊息內容內容內容內容",
                "DisplayMessage1": "顯示訊息訊息訊息1",
                "DisplayMessage2": "顯示訊息訊息訊息2",
                "EmailSubject": "郵件主題主題主題主題",
                "EmailMessage": "郵件內容內容內容內容",
                "NotifyMessage": "通知訊息訊息訊息訊息",
                "SMSMessage": "簡訊內容內容內容內容",
                "CreateUserAccount": "ADMIN",
                "CreateDate": "2025-05-20T17:52:50",
                "ModifyUserAccount": "ADMIN",
                "ModifyDate": "2025-05-20T17:54:47",
                "ScheduleList": [
                    {
                        "Weekly": "1,2,3,4,5",
                        "StartTime": "08:00",
                        "EndTime": "18:00"
                    }
                ],
                "ContactList": [
                    {
                        "NotifyType": "Email",
                        "Source": "2",
                        "ContactCode": "BRUCETEST"
                    }
                ],
                "ThirdList": [
                    {
                        "NotifyType": "ENS",
                        "ThirdCode": "test001"
                    }
                ],
                "HeaderList": [
                    {
                        "NotifyCode": "BRUCETEST"
                    }
                ]
            }
        ]
    },
    "title": null,
    "innerMsg": null,
    "token": null
}</code></pre>
    </div>
    
    <h2>資料驗證規則</h2>
    
    <h3>區域代碼 (AreaCode)</h3>
    <ul>
        <li>必填</li>
        <li>必須存在於 Area 表中</li>
    </ul>
    
    <h3>服務代碼 (ServiceCode)</h3>
    <ul>
        <li>必填</li>
        <li>必須是有效的服務代碼</li>
    </ul>
    
    <h3>描述 (Description)</h3>
    <ul>
        <li>必填</li>
        <li>最大長度為 100 字符</li>
    </ul>
    
    <h3>建築物代碼 (BuildingCode)</h3>
    <ul>
        <li>選填</li>
        <li>如果提供，必須存在於 Building 表中</li>
    </ul>
    
    <h3>樓層代碼 (PlaneCode)</h3>
    <ul>
        <li>選填</li>
        <li>如果提供，必須存在於 Plane 表中</li>
    </ul>
    
    <h3>部門代碼 (DeptCode)</h3>
    <ul>
        <li>選填</li>
        <li>如果提供，必須存在於 Department 表中</li>
    </ul>
    
    <h3>物件類型 (ObjectType)</h3>
    <ul>
        <li>選填</li>
        <li>如果提供，必須是有效的物件類型</li>
    </ul>
    
    <h3>群組代碼 (GroupCode)</h3>
    <ul>
        <li>選填</li>
        <li>如果提供，必須存在於 Group 表中</li>
    </ul>
    
    <h3>啟用狀態 (Enable)</h3>
    <ul>
        <li>必填</li>
        <li>只能是 "Y" 或 "N"</li>
    </ul>
    
    <h3>啟用排程 (EnableSchedule)</h3>
    <ul>
        <li>必填</li>
        <li>只能是 "Y" 或 "N"</li>
    </ul>
    
    <h3>排程列表 (ScheduleList)</h3>
    <ul>
        <li>當 EnableSchedule 為 "Y" 時必填</li>
        <li>每個排程項目必須包含 Weekly、StartTime 和 EndTime</li>
        <li>Weekly 格式必須符合正則表達式 <code>^([0-6](,[0-6])*)?$</code></li>
        <li>StartTime 和 EndTime 格式必須符合時間格式 <code>^([01][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$</code></li>
    </ul>
    
    <h3>排除時段列表 (ExclusionPeriodList)</h3>
    <ul>
        <li>選填</li>
        <li>每個排除時段項目必須包含 Weekly、StartTime 和 EndTime</li>
        <li>Weekly 格式必須符合正則表達式 <code>^([0-6](,[0-6])*)?$</code></li>
        <li>StartTime 和 EndTime 格式必須符合時間格式 <code>^([01][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$</code></li>
    </ul>
    
    <h2>錯誤回應</h2>
    <p>當請求無效時，API 將返回 400 Bad Request 狀態碼，並在回應中包含詳細的錯誤信息：</p>
    
    <pre><code>{
  "httpStatus": 400,
  "result": false,
  "data": [
    {
      "index": 0,
      "code": "string",
      "errors": [
        {
          "index": 0,
          "code": "string",
          "error": "string",
          "message": "string",
          "innerMsg": "string",
          "details": null
        }
      ]
    }
  ]
}</code></pre>

</body>
</html>